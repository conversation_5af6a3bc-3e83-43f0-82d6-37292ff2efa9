using Microsoft.AspNetCore.Mvc;

namespace AngelwinResearch.WebUI.Areas.API.Controllers
{
    [Area("API")]
    public class TestController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult TCMTest()
        {
            return View();
        }

        public IActionResult StreamDemo()
        {
            return View();
        }

        public IActionResult PatientFlow()
        {
            return View();
        }

        public IActionResult DecodeTest()
        {
            return View();
        }

        [HttpGet]
        public IActionResult TestTCM()
        {
            return Json(new {
                message = "中医经方推荐API测试成功",
                timestamp = System.DateTime.Now,
                apiUrl = "/API/TCMPrescription/GetPrescriptionRecommendation"
            });
        }
    }
}
