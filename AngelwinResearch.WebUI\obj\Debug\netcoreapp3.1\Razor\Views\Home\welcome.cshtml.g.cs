#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\welcome.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "0473dc59416a6611269c82de1d30664635d16749104df5f2bc4b9fcf87120d41"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Home_welcome), @"mvc.1.0.view", @"/Views/Home/welcome.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"0473dc59416a6611269c82de1d30664635d16749104df5f2bc4b9fcf87120d41", @"/Views/Home/welcome.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Home_welcome : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\welcome.cshtml"
  
    ViewBag.Title = "首页";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0473dc59416a6611269c82de1d30664635d16749104df5f2bc4b9fcf87120d413714", async() => {
                WriteLiteral(@"
    <title>欢迎使用</title>
    <meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />
    <meta name=""description"" content=""Awesome Bubble Navigation with jQuery"" />
    <meta name=""keywords"" content=""jquery, circular menu, navigation, round, bubble"" />
    <link rel=""stylesheet"" href=""/css/welcome_style.css"" type=""text/css"" media=""screen"" />
    <link rel=""stylesheet"" href=""/layuiadmin/layui/css/layui.css"">
");
                WriteLiteral(@"
    <style>
			* {
				margin: 0;
				padding: 0;
			}

			body {
				font-family: Arial;
				background-color: #fff;
				background-image: url(/images/welcome/bg1.jpg);
				background-repeat: no-repeat;
				background-position: left bottom;
				background-size: cover;
				overflow: hidden;
			}

			.title {
				width: 460px;
				height: 119px;
				position: fixed;
				bottom: 30px;
				left: 30px;
				background: transparent url(/images/welcome/title.png) no-repeat top left;
			}

			a.back {
				background: transparent url(back.png) no-repeat top left;
				position: fixed;
				width: 150px;
				height: 27px;
				outline: none;
				bottom: 0px;
				left: 0px;
			}

			/* 侧边信息栏 */
			.right_info {
				margin-right: 2px;
				margin-left: 10px;

			}

			.info_list>li {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				padding: 0 15px;
				line-height: 40px;
				border-bottom: 1px dashed #b16897;
			}
");
                WriteLiteral(@"

			.info_title {
				font-size: 18px;
				font-weight: bold;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}

			/* 页脚 */
			.footer {
				width: 100%;
				padding-bottom: 15px;
				position: absolute;
				bottom: 0;
			}

			.foot_content {

				display: flex;
				flex-direction: row;
				justify-content: flex-end;
				align-items: center;
				margin-right: 15px;
			}

			.logo1 {
				width: 112px;
				height: 50px;
				margin-right: 10px;
			}

			.logo1 img {
				width: 100%;
			}

			.cp_info {
				line-height: 24px;
				color: #585d5e;
			}

    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0473dc59416a6611269c82de1d30664635d16749104df5f2bc4b9fcf87120d416926", async() => {
                WriteLiteral(@"

    <div class=""layui-row"">
        <div class=""layui-col-md12"">
            <div id=""content"">
                <div class=""title""></div>
                <div class=""navigation"" id=""nav"">
                    <div class=""item user"" lay-href=""/ReportingManage/PatientManage/Index"">
                        <img src=""/images/welcome/bg_user.png""");
                BeginWriteAttribute("alt", " alt=\"", 2656, "\"", 2662, 0);
                EndWriteAttribute();
                WriteLiteral(@" width=""300"" height=""300"" class=""circle"" />
                        <a class=""icon""></a>
                        <h2>科研患者管理</h2>
                    </div>
                    <div class=""item home"" lay-href=""/PatientDiscoveryManage/CRFDataDetails/Index"">
                        <img src=""/images/welcome/bg_home.png""");
                BeginWriteAttribute("alt", " alt=\"", 2986, "\"", 2992, 0);
                EndWriteAttribute();
                WriteLiteral(@" width=""300"" height=""300"" class=""circle"" />
                        <a class=""icon""></a>
                        <h2>填报结果下载</h2>
                    </div>
                    <div class=""item shop"">
                        <img src=""/images/welcome/bg_shop.png""");
                BeginWriteAttribute("alt", " alt=\"", 3260, "\"", 3266, 0);
                EndWriteAttribute();
                WriteLiteral(@" width=""300"" height=""300"" class=""circle"" />
                        <a class=""icon ""></a>
                        <h2>电子病历</h2>
                    </div>
                    <div class=""item camera"" >
                        <img src=""/images/welcome/bg_camera.png""");
                BeginWriteAttribute("alt", " alt=\"", 3538, "\"", 3544, 0);
                EndWriteAttribute();
                WriteLiteral(" width=\"300\" height=\"300\" class=\"circle\" />\r\n                        <a class=\"icon\"></a>\r\n                        <h2>检验样本管理</h2>\r\n                    </div>\r\n\t\t\t\t\t<div class=\"item consultation\">\r\n\t\t\t\t\t    <img src=\"/images/welcome/bg_consultation.png\"");
                BeginWriteAttribute("alt", " alt=\"", 3798, "\"", 3804, 0);
                EndWriteAttribute();
                WriteLiteral(@" width=""300"" height=""300"" class=""circle"" />
					    <a class=""icon""></a>
                        <h2>随访管理</h2>
					</div>
                </div>
            </div>

        </div>

    </div>


    <!-- 			<div class=""footer"">

            <div class=""foot_content"">
                <div class=""logo1"">
                    <img src=""../images/color.png"" alt="""">
                </div>
                <div class=""cp_info"">
                    <p>技术支持：北京天助盈通技术有限公司V1.0</p>
                    <p>Beijing Angelwin Technology Co.,Ltd.</p>

                </div>
            </div>

        </div> -->
    <!-- The JavaScript -->
    <script type=""text/javascript"" src=""/js/jquery-1.10.2.min.js""></script>
    <script type=""text/javascript"" src=""/js/jquery.easing.1.3.js""></script>
    <script type=""text/javascript"" src=""/layuiadmin/layui/layui.js""></script>



    <script>
        // 添加错误处理
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.erro");
                WriteLiteral(@"r('JavaScript Error:', {
                message: msg,
                source: url,
                line: lineNo,
                column: columnNo,
                error: error
            });

            // 特别处理cellRender错误
            if (msg && msg.indexOf('cellRender') !== -1) {
                console.error('cellRender error detected. This might be due to laydate version mismatch or incorrect configuration.');
            }

            return false;
        };

        layui.config({
            base: '/layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'flow', 'jquery', 'layer', 'form', 'element', 'carousel'], function () {
            try {
                var flow = layui.flow,
                    $ = layui.$,
                    layer = layui.layer,
                    form = layui.form,
                    element = layui.element,
                    carousel = layui.carousel;

                // 检查laydate是否正确加载
");
                WriteLiteral(@"
                if (typeof layui.laydate === 'undefined') {
                    console.warn('laydate module not loaded');
                }
            } catch (error) {
                console.error('Error initializing layui modules:', error);
                return;
            }
            var showflag = false;
            var showflag1 = false;
            $('#showbtn').on('click', function () {
                if (showflag) {
                    showflag = false
                    $(""#showbtn"").attr(""class"", ""iconfont icon-yanjing_bi "");
                }
                else {
                    showflag = true;
                    $(""#showbtn"").attr(""class"", ""iconfont icon-browse"");

                }
                GetReportShowLis();

            });
            $('#showbtn1').on('click', function () {
                if (showflag1) {
                    showflag1 = false
                    $(""#showbtn1"").attr(""class"", ""iconfont icon-yanjing_bi "");
                }
");
                WriteLiteral(@"                else {
                    showflag1 = true;
                    $(""#showbtn1"").attr(""class"", ""iconfont icon-browse"");

                }

                //GetNotifyMessage();
            });

            function getUlH() {
                var cadHeader = $("".layui-card-header"").height();
                var windowH = $(window).height();
                var ulH = (windowH / 2) - cadHeader - 50
                $("".info_wrap"").css(""height"", ulH + ""px"");
            }
            getUlH();
            function changeHeight() {
                var winH = $(window).height();
                $(""#content"").css(""height"", winH + ""px"");
            }
            changeHeight();
            $("".right_info"").css(""display"", """");
            $("".fav"").find('img').stop().animate({
                'width': '200px',
                'height': '200px',
                'top': '-25px',
                'left': '-25px',
                'opacity': '1.0'
            }, 500, 'easeOutBack',");
                WriteLiteral(@" function () {

            });
            $('#nav > div').hover(
                function () {
                    var $this = $(this);
                    $this.find('img').stop().animate({
                        'width': '200px',
                        'height': '200px',
                        'top': '-25px',
                        'left': '-25px',
                        'opacity': '1.0'
                    }, 500, 'easeOutBack', function () {

                    });

                    $this.find('a:first,h2').addClass('active');
                },
                function () {
                    var $this = $(this);

                    $this.find('ul').fadeOut(500);

                    $this.find('img').stop().animate({
                        'width': '52px',
                        'height': '52px',
                        'top': '0px',
                        'left': '0px',
                        'opacity': '0.1'
                    }, 5000, 'easeOutBack');

");
                WriteLiteral(@"                    $this.find('a:first,h2').removeClass('active');
                });

            $(window).resize(function () {
                getUlH();
                changeHeight();
            });

            //报告隐藏显示通知
            function GetReportShowLis() {
                try {
                    $.ajax({
                        url: ""/Home/GetReportShowLis"",
                        type: ""post"",
                        datatype: 'json',
                        data: { ""ShowFlag"": showflag },
                        success: function (re) {
                        $(""#test2"").html("""");
                        //<li>
                        //    <p>0010019 张** 报告可见 <b>2022-10-20</b></p>
                        //    <dutton class=""layui-btn layui-btn-xs"">已知晓</dutton>
                        //</li>
                        var htmlstr = '<div carousel-item>';
                        if (re.code == 0) {
                            if (re.result != null && re.result != und");
                WriteLiteral(@"efined && re.result.length > 0) {

                                for (var i = 0; i < re.result.length; i++) {

                                    if (i == 0 || i == 10 || i == 20) {
                                        htmlstr += '<ul class=""info_list""> ';
                                    }
                                    htmlstr += '<li><p>' + re.result[i].patientID + "" "" + re.result[i].contents + '至 <b>' + re.result[i].crontime+'</b></p><dutton class=""layui-btn layui-btn-xs"">已知晓</dutton></li>'
                                    if (i == 9 || i == 19 || i == 29) {
                                        htmlstr += '</ul> ';
                                    }
                                }

                            }
                            else { htmlstr += '<ul ></ul>  '; }
                        }
                        else {
                            htmlstr += '<ul ></ul>  ';
                        }


                        htmlstr += '</div>';
   ");
                WriteLiteral(@"                     $(""#test2"").html(htmlstr);
                            try {
                                carousel.render({
                                    elem: '#test2',
                                    width: '100%' //设置容器宽度
                                    ,
                                    arrow: 'none' //始终显示箭头
                                    ,
                                    anim: 'default' //切换动画方式
                                    ,
                                    'autoplay': true
                                });
                                getUlH();
                                form.render();
                            } catch (carouselError) {
                                console.error('Error rendering carousel:', carouselError);
                            }
                        }, error: function (xhr, status, error) {
                            console.error('AJAX Error in GetReportShowLis:', error);
                        ");
                WriteLiteral(@"    layer.msg(""获取失败！"");
                        }
                    });
                } catch (error) {
                    console.error('Error in GetReportShowLis function:', error);
                }
            }
            //随访通知
            function GetNotifyMessage() {
                try {
                    $.ajax({
                        url: ""/Home/GetNotifyMessage"",
                        type: ""post"",
                        datatype: 'json',
                        data: { ""ShowFlag"": showflag1 },
                        success: function (re) {
                        $(""#test1"").html("""");
                        //<li>
                        //    <p>0010019 张** 报告可见 <b>2022-10-20</b></p>
                        //    <dutton class=""layui-btn layui-btn-xs"">已知晓</dutton>
                        //</li>
                        var htmlstr = '<div carousel-item>';
                        if (re.code == 0) {
                            if (re.result != null && re.res");
                WriteLiteral(@"ult != undefined && re.result.length > 0) {

                                for (var i = 0; i < re.result.length; i++) {

                                    if (i == 0 || i == 10 || i == 20) {
                                        htmlstr += '<ul class=""info_list""> ';
                                    }
                                    htmlstr += '<li><p>' + re.result[i].patientID + "" "" + re.result[i].contents + ' <b>' + re.result[i].crontime + '</b></p><dutton class=""layui-btn layui-btn-xs"">已知晓</dutton></li>'
                                    if (i == 9 || i == 19 || i == 29) {
                                        htmlstr += '</ul> ';
                                    }
                                }

                            }
                            else { htmlstr += '<ul ></ul>  '; }
                        }
                        else {
                            htmlstr += '<ul ></ul>  ';
                        }


                        htmlstr += '</");
                WriteLiteral(@"div>';
                        $(""#test1"").html(htmlstr);
                            try {
                                carousel.render({
                                    elem: '#test1',
                                    width: '100%' //设置容器宽度
                                    ,
                                    arrow: 'none' //始终显示箭头
                                    ,
                                    anim: 'default' //切换动画方式
                                    ,
                                    'autoplay': true
                                });
                                getUlH();
                                form.render();
                            } catch (carouselError) {
                                console.error('Error rendering carousel in GetNotifyMessage:', carouselError);
                            }
                        }, error: function (xhr, status, error) {
                            console.error('AJAX Error in GetNotifyMessage:', er");
                WriteLiteral(@"ror);
                            layer.msg(""获取失败！"");
                        }
                    });
                } catch (error) {
                    console.error('Error in GetNotifyMessage function:', error);
                }
            }
            //GetReportShowLis();
            //GetNotifyMessage();




        });
    </script>


    <script>
        // 参数加*函数
        function secretinfo(text, text_type) {
            // 1.姓名 2.出生日期 3.身份证 4.联系方式
            var testlen = text.length;
            var star = """"
            if (text_type == 1) { // 1.姓名
                for (var i = 0; i < testlen - 1; i++) {
                    star += ""*""
                }
                var text = text.substring(1, -1) + star
                console.log(text)
                return text

            } else if (text_type == 2) { //2.出生日期
                for (var i = 0; i < 4; i++) {
                    star += ""*""
                }
                text = star + text.slice(");
                WriteLiteral(@"4)
                console.log(text)
                return text

            } else if (text_type == 3) { //3.身份证
                for (var i = 0; i < testlen - 6; i++) {
                    star += ""*""
                }
                text = text.substring(6, -1) + star
                console.log(text)
                return text

            } else if (text_type == 4) { //4.联系方式
                for (var i = 0; i < 4; i++) {
                    star += ""*""
                }
                text = text.slice(0, -4) + star
                console.log(text)
                return text

            } else {
                console.log(text)
                return text
            }
        }
        /*secretinfo(""13072168888"", 4)*/
    </script>


");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
