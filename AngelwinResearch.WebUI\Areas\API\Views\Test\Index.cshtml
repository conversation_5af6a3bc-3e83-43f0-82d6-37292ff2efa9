@{
    ViewData["Title"] = "API测试页面";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>API功能测试</h2>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">中医经方推荐API</h5>
                    <p class="card-text">测试中医经方推荐功能是否正常工作</p>
                    
                    <div class="mb-3">
                        <a href="/API/TCMPrescription/Index" class="btn btn-primary" target="_blank">
                            <i class="fas fa-leaf"></i>
                            打开中医经方推荐页面
                        </a>
                        
                        <button id="testApiBtn" class="btn btn-secondary ml-2">
                            <i class="fas fa-flask"></i>
                            测试API连接
                        </button>
                    </div>
                    
                    <div id="testResult" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <h6>测试结果：</h6>
                            <pre id="resultContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#testApiBtn').click(function() {
                $.get('/API/Test/TestTCM')
                    .done(function(response) {
                        $('#resultContent').text(JSON.stringify(response, null, 2));
                        $('#testResult').show();
                    })
                    .fail(function(xhr, status, error) {
                        $('#resultContent').text('测试失败: ' + error);
                        $('#testResult').show();
                    });
            });
        });
    </script>
}
