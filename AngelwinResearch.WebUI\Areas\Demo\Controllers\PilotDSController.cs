using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using Common.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace AngelwinResearch.WebUI.Areas.Demo.Controllers
{
    [Area("Demo")]
    public class PilotDSController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinResearchDbContext db;
        public PilotDSController(IConfiguration configuration, AngelwinResearchDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }

        public IActionResult Index()
        {
            return View();
        }

      
        [SSE]
        public async Task<IActionResult> GetAgentSpeech1(string Speech, string xAppID)
        {
            var modelType = "deepseek-Agent";
            string mResult = "";
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
         
            string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"];
            string apiUrl =  Configuration[$"GPTSetting:{modelType}:apiUrl"];

            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);
            httpClient.BaseAddress = new Uri(apiUrl);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            dynamic answer = new ExpandoObject();

            //  answer.userInput =  Speech;
            // answer.type = "transfer_method";
            answer.question = Speech;
            var mAsk = new Ask();
             mAsk.inputs = answer;
          
            mAsk.response_mode = "streaming";
            mAsk.user = "user";
            var mstr = JsonConvert.SerializeObject(mAsk);
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                //Content = new StringContent(mstr, Encoding.UTF8, "application/json")
                Content = new StringContent(mstr, Encoding.UTF8, "application/json")
            };

            string mAllLine = "";
            using var APIResponse = await httpClient.SendAsync(request,HttpCompletionOption.ResponseHeadersRead);
            if (APIResponse.IsSuccessStatusCode)
            {
                var stream = await APIResponse.Content.ReadAsStreamAsync();
                var ResponseContent = "";
                var streamReader = new StreamReader(stream, Encoding.UTF8);

               // var ddd = await streamReader.ReadLineAsync();
                while (!streamReader.EndOfStream)
                {

                    var line = await streamReader.ReadLineAsync();
                    line = System.Text.RegularExpressions.Regex.Unescape(line);
                    mAllLine = mAllLine+ line+"\n\n";
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        if (line.StartsWith("data:"))
                        {
                            
                            line = line.Substring(5, line.Length - 5);
                            if (line.Contains("event") && line.Contains("text_chunk"))
                            {
                                //if (line.Contains("text_chunk"))
                                {
                                    try
                                    {
                                        var mDataTemp = JsonConvert.DeserializeObject<Message>(line);

                                        if (mDataTemp.@event == "text_chunk")
                                        {

                                            mResult = mDataTemp.data.text;
                                            ResponseContent += mResult;
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, isAgentResult = false, AgentResult = "", Id = mDataTemp.workflow_run_id })}\n\n");
                                            await response.Body.FlushAsync();

                                        }
                                    }
                                    catch(Exception exp)
                                    {
                                        LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容异常:" + line+"\n");
                                    }
                             
                                }
                               
                               // await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = false, AgentResult = "", Id = "" })}\n\n");
                              //  await response.Body.FlushAsync();

                            }

                            if (line.Contains("outputs"))
                            {
                                try
                                {
                                    var mDataTemp = JsonConvert.DeserializeObject<Message>(line);
                                    if (mDataTemp.data.outputs != null)
                                    {
                                        if (mDataTemp.data.outputs.result != null)
                                        {
                                            var mSlices = new List<Slices>();
                                            foreach (var item in mDataTemp.data.outputs.result)
                                            {
                                                var mSlice = new Slices();


                                                mSlice.score = item.metadata.score;
                                                mSlice.text = item.content;
                                                mSlice.source = item.metadata.document_name;
                                                mSlices.Add(mSlice);

                                            }
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = true, AgentResult = mSlices, Id = mDataTemp.workflow_run_id })}\n\n");
                                            await response.Body.FlushAsync();
                                        }

                                    }
                                }
                                catch(Exception exp)
                                {
                                    LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容异常:" + line + "\n");
                                }
                        

                            }
                        }

                    }


                    

                }
                var resultAll = new
                {
                    code = 100,
                    chart = 1,
                    msg = "",

                    Id =""
                };

                LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容:" + mAllLine);
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            else
            {
                return Json(new { code = -100, msg ="" });
            }
        }

        [SSE]
        public async Task<IActionResult> GetAgentSpeech(string Speech, string xAppID)
        {
            var modelType = "deepseek-Agent";
            string mResult = "";
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");

            string apiKey = "ApsQZTCgYrcIsYf681eIL2CBqC53soyR";//Configuration[$"GPTSetting:{modelType}:ApiKey"];
            string apiUrl = "https://aiworker.aminer.cn/aicoapi/gateway/v2/chatbot/api_run/1754016307_9f6ba609-8b62-4daf-9347-b53731edcc4e"; //Configuration[$"GPTSetting:{modelType}:apiUrl"];

            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);
            httpClient.BaseAddress = new Uri(apiUrl);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            var requestBody = new
            {
                doc_list = new string[] { },
                image_url = "",
                query = $"{Speech}",
                session_id = "",
                stream = true
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = new StringContent(json, Encoding.UTF8, "application/json")
            };

            string mAllLine = "";
            using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
            if (APIResponse.IsSuccessStatusCode)
            {
                var stream = await APIResponse.Content.ReadAsStreamAsync();
                var ResponseContent = "";
                var streamReader = new StreamReader(stream, Encoding.UTF8);

                // var ddd = await streamReader.ReadLineAsync();
                while (!streamReader.EndOfStream)
                {

                    var line = await streamReader.ReadLineAsync();
                    line = System.Text.RegularExpressions.Regex.Unescape(line);
                    mAllLine = mAllLine + line + "\n\n";
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        if (line.StartsWith("data:"))
                        {

                            line = line.Substring(5, line.Length - 5);
                            if (line.Contains("event") && line.Contains("text_chunk"))
                            {
                                //if (line.Contains("text_chunk"))
                                {
                                    try
                                    {
                                        var mDataTemp = JsonConvert.DeserializeObject<Message>(line);

                                        if (mDataTemp.@event == "text_chunk")
                                        {

                                            mResult = mDataTemp.data.text;
                                            ResponseContent += mResult;
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, isAgentResult = false, AgentResult = "", Id = mDataTemp.workflow_run_id })}\n\n");
                                            await response.Body.FlushAsync();

                                        }
                                    }
                                    catch (Exception exp)
                                    {
                                        LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容异常:" + line + "\n");
                                    }

                                }

                                // await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = false, AgentResult = "", Id = "" })}\n\n");
                                //  await response.Body.FlushAsync();

                            }

                            if (line.Contains("outputs"))
                            {
                                try
                                {
                                    var mDataTemp = JsonConvert.DeserializeObject<Message>(line);
                                    if (mDataTemp.data.outputs != null)
                                    {
                                        if (mDataTemp.data.outputs.result != null)
                                        {
                                            var mSlices = new List<Slices>();
                                            foreach (var item in mDataTemp.data.outputs.result)
                                            {
                                                var mSlice = new Slices();


                                                mSlice.score = item.metadata.score;
                                                mSlice.text = item.content;
                                                mSlice.source = item.metadata.document_name;
                                                mSlices.Add(mSlice);

                                            }
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = true, AgentResult = mSlices, Id = mDataTemp.workflow_run_id })}\n\n");
                                            await response.Body.FlushAsync();
                                        }

                                    }
                                }
                                catch (Exception exp)
                                {
                                    LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容异常:" + line + "\n");
                                }


                            }
                        }

                    }




                }
                var resultAll = new
                {
                    code = 100,
                    chart = 1,
                    msg = "",

                    Id = ""
                };

                LoggerHelper.WriteInfo("其他日志", $"PilotDS返回会话内容:" + mAllLine);
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            else
            {
                return Json(new { code = -100, msg = "" });
            }
        }

        #region AICO Model
        public class AICOMessage
        {
            public string @event { get; set; }
            public string session_id { get; set; }
            public string step_id { get; set; }

            public Data data { get; set; }
        }
        public class AICOData
        {
            public string text { get; set; }

            public output outputs { get; set; }
        }
        #endregion

        public class Slices
        {
            public string text { get; set; }

            public string document_id { get; set; }
            public string position { get; set; }
            public string line { get; set; }
            public string sheet_name { get; set; }

            public string score { get; set; }
            public string source { get; set; }

        }
        public  class Message
        {
            public string @event{ get;set; }
            public string workflow_run_id { get; set; }

            public string task_id { get; set; }

            public Data data { get; set; }
        }

        public  class Data 
        {
            public string text { get; set; }

            public output outputs { get; set; }
        }

        public class Ask
        {
            
             public object inputs { get; set; }
            public string response_mode { get; set; }
            public string user { get; set; }
        }


        public class output
        {

            public List<Result> result { get; set; } 

        } 


        public  class Result
        {
           public Metadata metadata { get; set; }
           public string content { get; set; }

         
        }

        public  class Metadata
        {
            public  string document_name { get; set; }
            public  string score { get; set; }
        }

    }
}
