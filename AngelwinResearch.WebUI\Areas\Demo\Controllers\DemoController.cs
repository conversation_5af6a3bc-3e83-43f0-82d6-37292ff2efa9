﻿using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using Common.Tools;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Areas.Demo.Controllers
{
    [Authorizing]
    [Area("Demo")]
    public class DemoController : Controller
    {
        private readonly AngelwinResearchDbContext db;
        private IConfiguration config { get; }
        private IWebHostEnvironment env;
        public DemoController(AngelwinResearchDbContext _db, IConfiguration _config, IWebHostEnvironment _env)
        {
            db = _db;
            config = _config;
            env = _env;
        }
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> ImgDataExtr()
        {

            LoggerHelper.WriteInfo("其他日志", $"ImgDataExtr:开始");
            var modelType = config["AppSettings:VLmodelType"];
            #region 读取配置文件
            string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
            string model = config[$"GPTSetting:{modelType}:Model"];
            string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];

            string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
            int maxTokens = 4000;
            if (!string.IsNullOrEmpty(maxTokenStr))
            {
                int.TryParse(maxTokenStr, out maxTokens);
            }

            HttpClientHandler handler = null;
            string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
            if (!string.IsNullOrWhiteSpace(webProxy))
            {
                handler = new HttpClientHandler()
                {
                    Proxy = new WebProxy(webProxy)
                };
            }
            else
            {
                handler = new HttpClientHandler() { };
            }
            #endregion
            string endpoint = "chat/completions";
            dynamic history = new List<dynamic>();
            var requestMsgTime = System.DateTime.Now;

            var okList = new List<string>();
            var errorList = new List<string>();
            // 指定要搜索的文件夹路径
            string folderPath = System.IO.Path.Combine(env.WebRootPath, "okjingzi/pic");
            // 获取文件夹下所有PDF文件
            string[] imgFiles = Directory.GetFiles(folderPath, "*.jpg");
            if (imgFiles.Length > 0)
            {
                var i = 0;
                foreach (string imgFile in imgFiles)
                {
                    if (i >= 10) break;
                    i++;
                    var shortName = Path.GetFileNameWithoutExtension(imgFile);
                    var ListImg = new List<string>();
                    ListImg.Add(imgFile);
                    var ResponseContent = "";
                    var contentStr = "";

                    #region 提示词
                    var prompt = @"
<角色>
角色：你是数据格式化专家。
</角色>
<任务>
任务：将用户提供的眼科报告特征变量按照指定的CSV格式进行整理和输出。
</任务>
<背景>
背景：用户需要将" + shortName + @"眼科报告中的特征变量提取并格式化为CSV格式，以便于数据存储和分析。
</背景>
<执行要求>
执行要求：
1. 严格按照用户提供的字段顺序进行排列。
2. 使用 ""|"" 符号作为字段分隔符。
3. 确保输出内容简洁，不包含任何解释性文字。
4. 保持数据的准确性和一致性。
5. 提取的值必须完整,需要包含数值和单位；
</执行要求>
<输出要求>
输出要求：
1. 输出格式为CSV，字段包括：序号、患者姓名、日期、部位、平K、陡K、ΔK、平面e²、斜面e²、8毫米凹陷差距、垂直Q、陡e、陡轴e²、角膜表面规则性指数、泪膜表面质量、平e、区域3毫米平面1能力、区域3毫米平面2能力、区域3毫米斜面1能力、区域3毫米斜面2能力、区域5毫米平面1能力、区域5毫米平面2能力、区域5毫米斜面1能力、区域5毫米斜面2能力、区域7毫米平面1能力、区域7毫米平面2能力、区域7毫米斜面1能力、区域7毫米斜面2能力、水平e、水平Q、中央泪膜表面质量、角膜表面非对称性指数、IS指数。
2. 各字段之间用 ""|"" 符号隔开。
3. 不包含任何额外的解释性文字。
</输出要求>
<输入>
用户输入：以下是" + shortName + @"眼科报告中提取特征变量,输出格式串要求如下：csv格式输出: 序号、患者姓名、日期、部位、平K、陡K、ΔK、平面e²、斜面e²、8毫米凹陷差距、垂直Q、陡e、陡轴e²、角膜表面规则性指数、泪膜表面质量、平e、区域3毫米平面1能力、区域3毫米平面2能力、区域3毫米斜面1能力、区域3毫米斜面2能力、区域5毫米平面1能力、区域5毫米平面2能力、区域5毫米斜面1能力、区域5毫米斜面2能力、区域7毫米平面1能力、区域7毫米平面2能力、区域7毫米斜面1能力、区域7毫米斜面2能力、水平e、水平Q、中央泪膜表面质量、角膜表面非对称性指数、IS指数；；中间用 | 符号隔开；不需要附带任何解释文字，只需要csv格式数据；
</输入>";
                    #endregion

                    var httpClient = new HttpClient(handler);
                    httpClient.BaseAddress = new Uri(apiUrl);
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    history.Add(new { role = "user", content = getContentList(modelType, prompt, ListImg) });
                    dynamic requstDTO = new ExpandoObject();
                    requstDTO.model = model;
                    requstDTO.messages = history;
                    requstDTO.stream = true;

                    var requestBody = JsonConvert.SerializeObject(requstDTO);
                    var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                    {
                        Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                    };

                    using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                    {
                        if (APIResponse.IsSuccessStatusCode)
                        {
                            var stream = await APIResponse.Content.ReadAsStreamAsync();
                            var streamReader = new StreamReader(stream);
                            while (!streamReader.EndOfStream)
                            {
                                var line = await streamReader.ReadLineAsync();
                                if (!string.IsNullOrWhiteSpace(line))
                                {
                                    if (line != "data: [DONE]")
                                    {
                                        if (line.StartsWith("data:"))
                                            line = line.Substring(5, line.Length - 5);
                                        var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                        var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                        if (delta != null && ((JContainer)delta).Count > 0)
                                        {
                                            if (delta["content"] != null) //在不同的api下第一个未必是content，调整支持不同的api
                                            {
                                                contentStr = delta["content"].ToString();
                                                ResponseContent += contentStr;
                                            }

                                        }
                                        if (finish_reason != null)
                                        {
                                            var resultfinish = finish_reason as JProperty;
                                        }
                                    }
                                }
                            }
                            LoggerHelper.WriteInfo("其他日志", $"{ResponseContent}");
                            okList.Add(ResponseContent);

                            // 3. 处理完毕后删除文件
                            System.IO.File.Delete(imgFile);
                        }
                        else
                        {
                            LoggerHelper.WriteInfo("其他日志", $"报错");
                            errorList.Add(shortName);
                        }
                    }
                }
            }

            return Json(new { code = "0", msg = "成功", count = 0, data = okList, errorData = errorList });
        }

        private List<dynamic> getContentList(string modelType, string content, List<string> ListImg)
        {
            string folderPath = env.WebRootPath;
            var list = new List<dynamic>();
            dynamic requstDTO = new ExpandoObject();
            requstDTO.type = "text";
            requstDTO.text = content;
            list.Add(requstDTO);
            foreach (var str in ListImg)
            {

                var filePath = System.IO.Path.Combine(folderPath, str);
                byte[] imageBytes;
                using (FileStream fileStream = new FileStream(filePath, System.IO.FileMode.Open, System.IO.FileAccess.Read))
                {
                    BinaryReader binaryReader = new BinaryReader(fileStream);
                    imageBytes = binaryReader.ReadBytes((int)fileStream.Length);
                }
                // 将字节数组转换为Base64编码的字符串
                var Imgbase64 = Convert.ToBase64String(imageBytes);
                requstDTO = new ExpandoObject();
                requstDTO.type = "image_url";
                if (modelType.StartsWith("qwen"))   //
                {
                    var imageTypeIndex = str.LastIndexOf(".");
                    var format = str.Substring(imageTypeIndex + 1, str.Length - imageTypeIndex - 1);
                    requstDTO.image_url = new { url = $"data:image/{format};base64,{Imgbase64}" };
                }
                else
                {
                    requstDTO.image_url = new { url = Imgbase64 };
                }
                list.Add(requstDTO);
            }

            return list;
        }


        /// <summary>
        /// demo/demo/PdfConvertToImg
        /// </summary>
        /// <returns></returns>
        public IActionResult PdfConvertToImg()
        {
            try
            {

                // 指定要搜索的文件夹路径
                string folderPath = System.IO.Path.Combine(env.WebRootPath, "okjingzi");
                // 获取文件夹下所有PDF文件
                string[] pdfFiles = Directory.GetFiles(folderPath, "*.pdf");
                if (pdfFiles.Length > 0)
                {
                    foreach (string pdfFileName in pdfFiles)
                    {
                        var shortName = Path.GetFileNameWithoutExtension(pdfFileName);
                        var pdfPath = System.IO.Path.Combine(folderPath, pdfFileName);
                        // 读取文件内容
                        byte[] pdfBytes = System.IO.File.ReadAllBytes(pdfPath);
                        string base64String = Convert.ToBase64String(pdfBytes);
                        var option = new PDFtoImage.RenderOptions(400);
                        var imgList = PDFtoImage.Conversion.ToImages(base64String, null, option);
                        foreach (var bitmap in imgList)
                        {
                            using (var image = SKImage.FromBitmap(bitmap))
                            {

                                var ImgPath = System.IO.Path.Combine(folderPath, $"pic/{shortName}.jpg");
                                // 编码为PNG格式
                                using (var data = image.Encode(SKEncodedImageFormat.Jpeg, 900)) // 100表示PNG压缩质量
                                {
                                    // 保存到文件
                                    using (var stream = System.IO.File.OpenWrite(ImgPath))
                                    {
                                        data.SaveTo(stream);
                                    }
                                }
                            }
                        }
                    }
                }
                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = "保存文件报错！", data = ex });
            }
        }
    }
}
