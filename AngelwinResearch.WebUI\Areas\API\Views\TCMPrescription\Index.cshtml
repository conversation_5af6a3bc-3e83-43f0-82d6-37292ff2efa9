@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医经方推荐</title>
    <link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css">
    <style>
        body {
            background-image: url('~/images/bg_1.jpg');
            background-color: #f0f0f0; /* 图片加载失败时的备用背景色 */
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
            background-attachment: fixed; /* 固定背景，防止滚动时背景移动 */
            overflow: hidden;
        }

        /* 主页面内容样式 */
        .main-content {
            max-width: 980px;
            min-width: 635px;
            margin: 0 auto;
        }


        /* 头部患者信息 */
        .page_header {
            padding-top: 20px;
            margin-bottom: 5px;
        }

        .popup-header {
            padding: 15px;
            background: #5FB878;
            color: white;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .layui-card {
            border-radius: 6px;
        }

        .patient-info {
            margin: 10px 0;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
        }

        .patient-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin: 15px;
        }

        .status span {
            font-size: 20px;
            font-weight: bold;
            margin-right: 10px;
        }

        .admission_information {
            display: flex;
            flex-direction: row;
        }

        /* 折叠卡片 */
        .layui-collapse {
            border: none;
        }

        .card_group {
            overflow-y: auto;
        }

        .layui-colla-item {
            border-radius: 6px !important;
            border-left: none !important;
            border-right: none !important;
            margin-bottom: 8px !important;
            overflow: hidden; /* 确保圆角生效 */
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* 添加轻微阴影增强视觉效果 */
        }

            .layui-colla-item:first-child {
                border-top: none !important;
            }


        /* 当面板展开时，内容区域也应用圆角 */
        .layui-colla-content {
            padding: 15px;
            background-color: #f0f0f0;
        }

        /* 触发按钮 */
        .trigger-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 19891025;
        }



        /* 关闭按钮 */
        .close-btn {
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
        }

        /* 经方卡片内容 */

        .cards-container {
            border-radius: 6px;
            overflow: hidden;
        }

        .ai_group {
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
        }

        .table_group {
            margin-top: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
        }

        .formula-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .formula-info p {
            margin: 5px 0;
            line-height: 1.6;
        }

        /* 操作按钮容器 */
        .action-buttons {
            margin-top: 15px;
            text-align: right;
        }

        .color1 {
            color: #FF5722;
        }

        .color2 {
            color: #1E9FFF;
        }

        /* 加载状态样式 */
        .loading-content {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1E9FFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式标题 */
        @@media screen and (max-width: 768px) {
            .main-title

        {
            font-size: 20px;
        }

        }

        /* 在小屏幕上调整弹出组件大小 */
        @@media screen and (max-width: 480px) {
            .popup-container

        {
            width: calc(100% - 40px);
            bottom: 10px;
            right: 10px;
        }

        .trigger-btn {
            bottom: 10px;
            right: 10px;
        }

        }</style>

</head>
<body>
    <div class="layui-container main-content">
        <div class="layui-row page_header">
            <h1 class="main-title">中医经方推荐系统</h1>
            <div class="patient-info">
                <div class="patient-avatar">
                    <img src="~/images/user_icon.png" alt="" width="100%" height="100%">
                </div>
                <div>
                    <div class="status"><span>张一鸣</span><span>男</span><span>35岁</span></div>
                    <div class="admission_information"><p>住院号：<span>ZY20230512</span></p>丨<p>科室：<span>心血管内科</span></p></div>
                </div>
            </div>

        </div>



        <!-- 可折叠卡片容器 -->
        <div class="cards-container">
            <div class="layui-collapse card_group" id="prescriptionCards">
                <!-- 加载中状态 -->
                <div class="loading-content" id="loadingContent">
                    <div style="text-align: center; padding: 50px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px; color: #1E9FFF;"></i>
                        <p style="margin-top: 10px; color: #666;">正在分析症状，生成经方推荐...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
           layui.use(['element', 'layer','jquery','table'], function(){
                var element = layui.element,
                    layer = layui.layer
                    $ = layui.jquery,
                    table = layui.table;





                // 初始化折叠面板
                element.render('collapse');

                function setContainerH(){
                    var winh = $(window).height();
                    var topH = $('.page_header').height();
                    var containerH = winh - topH-40;

                    $('.card_group').css('height', containerH);
                }
                setContainerH();

                $(window).resize(function(){
                    setContainerH();
                });

                // 页面加载时获取症状参数并调用AI接口
                $(document).ready(function() {
                    // 从URL参数获取症状描述
                    var  symptomsStr= getUrlParameter('symptoms') || '近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠';
                    var diagnosis=getUrlParameter('diagnosis');
                    var symptoms=symptomsStr||diagnosis;
                    if (symptoms) {
                        getPrescriptionRecommendations(symptoms);
                    }
                });

                // 获取URL参数
                function getUrlParameter(name) {
                    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                    var results = regex.exec(location.search);
                    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
                }

                // 调用AI接口获取经方推荐
                function getPrescriptionRecommendations(symptoms) {
                    var source = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' + encodeURIComponent(symptoms) + '&sessionId=');
                    var allData = "";
                    var i = 0;
                    source.onmessage = function (event) {
                        var result = JSON.parse(event.data);
                        if (result.error) {
                            showError(result.error);
                            source.close();
                            return;
                        }
                        if(if==0){
                       
                        }else{
                        
                        }
                        if (result.type === 'chunk' && result.content) {
                            allData += result.content;
                            var htmlContent = marked.parse(allData);
                             //marked.parse(allData)
                        }
                        // else if (result.type === 'complete' && result.output) {
                        //     // 解析完整输出并生成卡片
                        //     parseAndCreateCards(result.output);
                        //     source.close();
                        // }
                    };

                    source.addEventListener('end', function (event) {
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        showError("连接出现错误，请重试");
                        source.close();
                    };
                }

                // 解析AI输出并创建卡片
                function parseAndCreateCards(output) {
                    // 隐藏加载状态
                    $('#loadingContent').hide();

                    // 解析AI输出，这里需要根据实际AI输出格式调整
                    var cards = parseAIOutput(output);

                    var cardsHtml = '';
                    cards.forEach(function(card, index) {
                        cardsHtml += createCardHtml(card, index);
                    });

                    $('#prescriptionCards').html(cardsHtml);
                    element.render('collapse');

                    // 渲染表格
                    cards.forEach(function(card, index) {
                        if (card.medicines && card.medicines.length > 0) {
                            renderMedicineTable(card.medicines, 'test' + (index + 1));
                        }
                    });
                }

                // 解析AI输出（根据实际AI输出格式调整）
                function parseAIOutput(output) {
                    // 这里是示例解析逻辑，需要根据实际AI输出格式调整
                    // 假设AI返回的是包含经方信息的文本，我们需要解析出结构化数据

                    // 临时示例：创建两个基于AI输出的卡片
                    return [
                        {
                            name: extractFromOutput(output, "处方名称") || "AI推荐方剂一",
                            source: "中医知识库",
                            composition: extractFromOutput(output, "处方内容") || extractFromOutput(output, "组成") || "根据AI分析生成的药物组成",
                            efficacy: extractFromOutput(output, "功效") || "根据AI分析生成的功效",
                            indications: extractFromOutput(output, "主治") || "根据AI分析生成的主治",
                            usage: extractFromOutput(output, "用法") || "根据AI分析生成的用法",
                            applications: extractFromOutput(output, "现代应用") || "根据AI分析生成的现代应用",
                            analysis: extractFromOutput(output, "分析内容") || "",
                            medicines: [
                                { id: 1, name: '酸枣仁', dosage: '15g', price: '25'},
                                { id: 2, name: '茯苓', dosage: '12g', price: '18' },
                                { id: 3, name: '知母', dosage: '9g', price: '12' },
                                { id: 4, name: '川芎', dosage: '6g', price: '15' },
                                { id: 5, name: '甘草', dosage: '6g', price: '8' }
                            ]
                        },
                        {
                            name: "AI推荐方剂二",
                            source: "deepseek",
                            composition: "根据AI分析生成的第二个方剂组成",
                            efficacy: "根据AI分析生成的第二个方剂功效",
                            indications: "根据AI分析生成的第二个方剂主治",
                            usage: "根据AI分析生成的第二个方剂用法",
                            applications: "根据AI分析生成的第二个方剂现代应用",
                            analysis: "",
                            medicines: []
                        }
                    ];
                }

                // 从AI输出中提取特定信息
                function extractFromOutput(output, keyword) {
                    // 简单的文本提取逻辑，可以根据需要改进
                    var regex = new RegExp(keyword + '[：:](.*?)(?=\\n|$)', 'i');
                    var match = output.match(regex);
                    return match ? match[1].trim() : null;
                }

                // 创建卡片HTML
                function createCardHtml(card, index) {
                    var badgeClass = index === 0 ? 'layui-bg-red' : '';
                    var badgeText = index === 0 ? '<span class="layui-badge ' + badgeClass + '">推荐</span>' : '';
                    var showClass = index === 0 ? 'layui-show' : '';
                    var colorClass = card.source === '中医知识库' ? 'color1' : 'color2';

                    var tableHtml = '';
                    if (card.medicines && card.medicines.length > 0) {
                        tableHtml = `
                            <div class="table_group">
                                <div class="formula-title">经方合计</div>
                                <table class="layui-hide" id="test${index + 1}" lay-filter="test${index + 1}"></table>
                            </div>
                        `;
                    }

                    var analysisHtml = '';
                    if (card.analysis) {
                        analysisHtml = `<p><strong>分析：</strong>${card.analysis}</p>`;
                    }

                    return `
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">${card.name} ${badgeText}</h2>
                            <div class="layui-colla-content ${showClass}">
                                <div class="ai_group">
                                    <div class="formula-title">由<span class="${colorClass}">${card.source}</span>生成</div>
                                    <div class="formula-info">
                                        <p><strong>组成：</strong>${card.composition}</p>
                                        <p><strong>功效：</strong>${card.efficacy}</p>
                                        <p><strong>主治：</strong>${card.indications}</p>
                                        <p><strong>用法：</strong>${card.usage}</p>
                                        ${card.applications ? '<p><strong>现代应用：</strong>' + card.applications + '</p>' : ''}
                                        ${analysisHtml}
                                    </div>
                                </div>
                                ${tableHtml}
                                <div class="action-buttons">
                                    <button class="layui-btn layui-btn-sm layui-bg-blue"><i class="layui-icon layui-icon-ok"></i>选择此方</button>
                                    <button class="layui-btn layui-btn-sm layui-bg-red"><i class="layui-icon layui-icon-close"></i>不采纳</button>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // 渲染药材表格
                function renderMedicineTable(medicines, tableId) {
                    table.render({
                        elem: '#' + tableId,
                        title: '费用汇总',
                        height: '320',
                        cols: [[
                            {field:'id', title:'ID', width:90, unresize: true, sort: true, totalRowText: '合计费用'},
                            {field: 'name', title: '药品', minWidth: 120},
                            {field: 'dosage', title: '计量', width: 150},
                            {field: 'price', title: '单价', width: 160, sort: true, totalRow: true,
                             templet: function (d) {
                                 return '￥' + d.price;
                             }}
                        ]],
                        data: medicines,
                        page: true,
                        limits: [5, 7, 10],
                        limit: 5,
                        totalRow: true,
                        response: {
                            statusCode: 200
                        }
                    });
                }

                // 显示错误信息
                function showError(message) {
                    $('#prescriptionCards').html(`
                        <div style="text-align: center; padding: 50px;">
                            <i class="layui-icon layui-icon-close" style="font-size: 30px; color: #FF5722;"></i>
                            <p style="margin-top: 10px; color: #FF5722;">错误：${message}</p>
                            <button class="layui-btn layui-btn-sm" onclick="location.reload()">重试</button>
                        </div>
                    `);
                }
        });

    </script>
</body>
</html>