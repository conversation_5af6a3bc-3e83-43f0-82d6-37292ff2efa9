@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名老中医经方传承智能助手</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/js/marked.min.js"></script>
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .userwrap {
            padding: 10px 15px;
            background-color: rgb(239 246 255 / 0.7);
            border-bottom: 1px solid #ccc;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .row_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .userinfo {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .userinfo_val {
            padding: 0 5px;
        }

        .doc_check {
            color: black;
            font-weight: bold;
        }

        .prompt {
            margin: 5px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .operating {
            width: 100%;
            position: fixed;
            bottom: 0;
        }

        .input_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px;
            background-color: #fff;
            border: 1px solid #7a4d7b;
            border-radius: 6px;
            overflow: hidden;
        }

            .input_wrap .layui-icon {
                color: #7a4d7b;
                padding: 12px;
            }

            .input_wrap .layui-input {
                border: none;
                padding-left: 0;
            }

                .input_wrap .layui-input:hover {
                    border: none;
                }

        .layui-icon-release {
            background-color: #7a4d7b;
            color: #fff !important;
            cursor: pointer;
        }

        /*对话区*/
        .dialog_box {
            padding: 10px;
            overflow-y: auto;
            font-size: 16px;
        }

        .quizzer {
            padding: 10px 0;
            display: flex;
            justify-content: flex-end;
        }

        .text_wrap {
            width: 80%;
            background-color: rgba(255, 251, 235, 0.7);
            padding: 10px;
            border-radius: 6px 0 6px 6px;
        }

        .answer {
            width: 100%;
        }

        .answer_inner {
            min-height: 100px;
            padding: 10px;
            background-color: #fff;
            border-radius: 6px;
        }

        .prescription-content {
            font-family: "Microsoft YaHei", Arial, "Helvetica Neue", sans-serif !important;
            line-height: 1.6;
        }

        .prescription-section {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }

        .prescription-title {
            font-weight: bold;
            color: #2c5530;
            margin-bottom: 8px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="chat layui-form">
        <div class="userwrap row_wrap">
            <div class="userinfo">
                <div style="width:20px;"></div>
                <div class="userinfo_val doc_check">
                    <i class="layui-icon layui-icon-triangle-r" style="font-size: 20px; color: green;"></i>
                    中医经方推荐系统 - 请描述患者症状
                </div>
            </div>
        </div>
        <div class="operating layui-form">
            <div class="prompt">
                <div>你可以和我说：</div>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠')">失眠症状</button>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('胸闷气短，心悸怔忡，舌淡苔白，脉细弱')">心悸症状</button>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('头痛眩晕，耳鸣如蝉，腰膝酸软，舌红少苔')">头痛眩晕</button>
            </div>
            <div class="input_wrap">
                <div class="layui-input-split layui-input-prefix">
                    <i class="layui-icon layui-icon-dialogue"></i>
                </div>
                <input type="text" placeholder="请输入患者症状描述..." class="layui-input" id="Input">
                <i class="layui-icon layui-icon-release" id="InputBtn"></i>
            </div>
        </div>

        <div class="dialog_box" id="dialog_box">
            <!-- 对话内容将在这里动态生成 -->
        </div>
    </div>

    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
        layui.use(['jquery', 'layer', 'form'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form;

            var xuhao = 1;
            var source = null;

            $(window).resize(function () {
                setDialogBoxH();
            });

            $(document).ready(function () {
                setDialogBoxH();
            });

            function setDialogBoxH() {
                var winH = $(window).height();
                var userwrapH = $(".userwrap").height();
                var operatingH = $(".operating").height();
                var DialogBox = winH - (userwrapH + operatingH) - 40;
                $(".dialog_box").css("height", DialogBox + "px");
            }

            let input_p = document.getElementById("Input");

            document.onkeydown = function () {
                if (event.keyCode == 13) {
                    GetPrescriptionRecommendation();
                }
            }

            // 设置输入框内容
            window.setInput = function(text) {
                $("#Input").val(text);
            }

            function GetPrescriptionRecommendation() {
                if ($.trim($("#Input").val()) == "") {
                    layer.msg("请输入症状描述！");
                    return;
                }

                var indexs = layer.load();
                var htm = `<div class="quizzer"><div class="text_wrap" id="answer">`;
                htm += `<p>` + $("#Input").val() + `</p></div></div>`;
                $(".dialog_box").append(htm);

                var symptoms = $("#Input").val();
                $("#Input").val("");

                // 关闭之前的连接
                if (source) {
                    source.close();
                }

                // 创建SSE连接
                source = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' + encodeURIComponent(symptoms) + '&sessionId=');
                var allData = "";
                var i = 0;
                var elementId = "A" + xuhao;

                source.onmessage = function (event) {
                    layer.close(indexs);
                    var result = JSON.parse(event.data);

                    if (result.error) {
                        layer.msg(result.error);
                        source.close();
                        return;
                    }

                    if (result.type === 'chunk' && result.content) {
                        // 流式内容
                        if (i == 0) {
                            var htm1Result = `<div class='answer'><div class='answer_inner'>
                                                <pre class='prescription-content' id='A` + xuhao + `'></pre>
                                              </div></div>`;
                            $(".dialog_box").append(htm1Result);
                        }

                        var btnloading = document.getElementById(elementId);
                        allData = allData + result.content;
                        var htmlContent = marked.parse(allData);
                        btnloading.innerHTML = htmlContent;
                        i = i + 1;
                    } else if (result.type === 'complete' && result.output) {
                        // 完整输出
                        var btnloading = document.getElementById(elementId);
                        var htmlContent = marked.parse(result.output);
                        btnloading.innerHTML = htmlContent;
                    } else if (result.code === 100) {
                        // 结束标志
                        // 可以在这里添加结束处理逻辑
                    }

                    resetHistoryscrollTop();
                };

                source.addEventListener('end', function (event) {
                    xuhao = xuhao + 1;
                    var result = JSON.parse(event.data);
                    if (result.code == 100) {
                        // 处理结束事件
                    } else {
                        layer.msg(result.msg);
                    }
                    source.close();
                }, false);

                source.onerror = function (event) {
                    layer.close(indexs);
                    xuhao = xuhao + 1;
                    layer.msg("连接出现错误，请重试");
                    source.close();
                };
            }

            $("#InputBtn").on("click", function () {
                GetPrescriptionRecommendation();
            });

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById("dialog_box");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 100);
                }
            }
        });
    </script>
</body>
</html>



        <!-- 可折叠卡片容器 -->
        <div class="cards-container">
            <div class="layui-collapse card_group">
                <!-- 第一个卡片（默认展开） -->
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">桂枝汤 <span class="layui-badge layui-bg-red">推荐</span></h2>
                    <div class="layui-colla-content layui-show">
                        <div class="ai_group">
                            <div class="formula-title">由<span class="color1">中医知识库</span>生成</div>
                            <div class="formula-info">
                                <p><strong>组成：</strong>桂枝9g、芍药9g、甘草6g、生姜9g、大枣3枚</p>
                                <p><strong>功效：</strong>解肌发表，调和营卫</p>
                                <p><strong>主治：</strong>外感风寒表虚证。头痛发热，汗出恶风，鼻鸣干呕，苔白不渴，脉浮缓或浮弱。</p>
                                <p><strong>用法：</strong>水煎服，温覆取微汗。</p>
                                <p><strong>现代应用：</strong>感冒、流行性感冒、原因不明的低热、产后或病后低热等属外感风寒表虚证者。</p>
                            </div>
                        </div>
                        <div class="table_group">
                            <div class="formula-title">经方合计</div>
                            <table class="layui-hide" id="test1" lay-filter="test1"></table>
                        </div>
                        <div class="action-buttons">
                            <button class="layui-btn layui-btn-sm layui-bg-blue"><i class="layui-icon layui-icon-ok"></i>选择此方</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red"><i class="layui-icon layui-icon-close"></i>不采纳</button>
                        </div>
                    </div>
                </div>


                <!-- 第二个卡片（默认展开） -->
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">小柴胡汤 </h2>
                    <div class="layui-colla-content ">
                        <div class="ai_group">
                            <div class="formula-title">由<span class="color2">deepseek</span>生成</div>
                            <div class="formula-info">
                                <p><strong>组成：</strong>麻黄9g、桂枝6g、杏仁6g、甘草3g</p>
                                <p><strong>功效：</strong>发汗解表，宣肺平喘</p>
                                <p><strong>主治：</strong>外感风寒表实证。恶寒发热，头身疼痛，鼻塞流涕，口不渴，舌苔薄白，脉浮紧。</p>
                                <p><strong>用法：</strong>水煎服，温服取微汗。</p>
                                <p><strong>注意事项：</strong>表虚自汗、阴虚盗汗及温病等情况慎用。</p>
                            </div>
                        </div>
                        <!-- <div class="table_group">
                            <div class="formula-title">经方合计</div>
                            <table class="layui-hide" id="test2" lay-filter="test2"></table>
                        </div> -->
                        <div class="action-buttons">
                            <button class="layui-btn layui-btn-sm layui-bg-blue"><i class="layui-icon layui-icon-ok"></i>选择此方</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red"><i class="layui-icon layui-icon-close"></i>不采纳</button>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>



    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
           layui.use(['element', 'layer','jquery','table'], function(){
                var element = layui.element,
                    layer = layui.layer
                    $ = layui.jquery,
                    table = layui.table;





                // 初始化折叠面板
                element.render('collapse');

                function setContainerH(){
                    var winh = $(window).height();
                    var topH = $('.page_header').height();
                    var containerH = winh - topH-40;

                    $('.card_group').css('height', containerH);
                }
                setContainerH();

                $(window).resize(function(){
                    setContainerH();
                });

                var data = [
                    { id: 1, name: '桂枝', dosage: '9g    ', price: '30'},
                    { id: 2, name: '芍药', dosage: '9g    ', price: '30' },
                    { id: 3, name: '甘草', dosage: '6g   ', price: '8' },
                    { id: 4, name: '生姜', dosage: '9g   ', price: '0.1' },
                    { id: 5, name: '大枣', dosage: '3枚   ', price: '1' }
                ];

                 //展示已知数据
                table.render({
                    elem: '#test1'
                    ,title: '费用汇总'
                    ,height: '320' //设置表格高度
                    ,cols: [[ //标题栏
                         {field:'id', title:'ID', width:90,unresize: true, sort: true, totalRowText: '合计费用'}
                        ,{field: 'name', title: '药品', minWidth: 120}
                        ,{field: 'dosage', title: '计量', width: 150}
                        ,{field: 'price', title: '单价', width: 160
                        ,sort: true
                        ,totalRow: true
                        ,templet: function (d) {
                            return '￥' + d.price;
                        }}
                    ]]
                    ,data: data
                    ,page: true //是否显示分页
                    ,limits: [5, 7, 10]
                    ,limit: 5 //每页默认显示的数量
                    ,totalRow: true//开启合计行
                    ,response: {
                        statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
                        }
                    ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                        return {
                            "code": res.status, //解析接口状态
                            "msg": res.message, //解析提示文本
                            "count": res.total, //解析数据长度
                            "data": res.rows.item //解析数据列表
                        };
                    }
                });

                             //展示已知数据
                table.render({
                    elem: '#test2'
                    ,title: '费用汇总'
                    ,height: '300' //设置表格高度
                    ,cols: [[ //标题栏
                         {field:'id', title:'ID', width:90,unresize: true, sort: true, totalRowText: '合计费用'}
                        ,{field: 'name', title: '药品', minWidth: 120}
                        ,{field: 'dosage', title: '计量', width: 150}
                        ,{field: 'price', title: '单价', width: 160
                        ,sort: true
                        ,totalRow: true
                        ,templet: function (d) {
                            return '￥' + d.price;
                        }}
                    ]]
                    ,data: data
                    ,page: true //是否显示分页
                    ,limits: [5, 7, 10]
                    ,limit: 5 //每页默认显示的数量
                    ,totalRow: true//开启合计行
                    ,response: {
                        statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
                        }
                    ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                        return {
                            "code": res.status, //解析接口状态
                            "msg": res.message, //解析提示文本
                            "count": res.total, //解析数据长度
                            "data": res.rows.item //解析数据列表
                        };
                    }
                });
        });

    </script>
</body>
</html>