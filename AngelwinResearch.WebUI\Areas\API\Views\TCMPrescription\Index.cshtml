@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医经方推荐</title>
    <link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css">
    <style>
        body {
            background-image: url('~/images/bg_1.jpg');
            background-color: #f0f0f0; /* 图片加载失败时的备用背景色 */
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
            background-attachment: fixed; /* 固定背景，防止滚动时背景移动 */
            overflow: hidden;
        }

        /* 主页面内容样式 */
        .main-content {
            max-width: 980px;
            min-width: 635px;
            margin: 0 auto;
        }


        /* 头部患者信息 */
        .page_header {
            padding-top: 20px;
            margin-bottom: 5px;
        }

        .popup-header {
            padding: 15px;
            background: #5FB878;
            color: white;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .layui-card {
            border-radius: 6px;
        }

        .patient-info {
            margin: 10px 0;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
        }

        .patient-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin: 15px;
        }

        .status span {
            font-size: 20px;
            font-weight: bold;
            margin-right: 10px;
        }

        .admission_information {
            display: flex;
            flex-direction: row;
        }

        /* 折叠卡片 */
        .layui-collapse {
            border: none;
        }

        .card_group {
            overflow-y: auto;
        }

        .layui-colla-item {
            border-radius: 6px !important;
            border-left: none !important;
            border-right: none !important;
            margin-bottom: 8px !important;
            overflow: hidden; /* 确保圆角生效 */
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* 添加轻微阴影增强视觉效果 */
        }

            .layui-colla-item:first-child {
                border-top: none !important;
            }


        /* 当面板展开时，内容区域也应用圆角 */
        .layui-colla-content {
            padding: 15px;
            background-color: #f0f0f0;
        }

        /* 触发按钮 */
        .trigger-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 19891025;
        }



        /* 关闭按钮 */
        .close-btn {
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
        }

        /* 经方卡片内容 */

        .cards-container {
            border-radius: 6px;
            overflow: hidden;
        }

        .ai_group {
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
        }

        .table_group {
            margin-top: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
        }

        .formula-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .formula-info p {
            margin: 5px 0;
            line-height: 1.6;
        }

        /* 操作按钮容器 */
        .action-buttons {
            margin-top: 15px;
            text-align: right;
        }

        .color1 {
            color: #FF5722;
        }

        .color2 {
            color: #1E9FFF;
        }

        /* 响应式标题 */
        @@media screen and (max-width: 768px) {
            .main-title

        {
            font-size: 20px;
        }

        }

        /* 在小屏幕上调整弹出组件大小 */
        @@media screen and (max-width: 480px) {
            .popup-container

        {
            width: calc(100% - 40px);
            bottom: 10px;
            right: 10px;
        }

        .trigger-btn {
            bottom: 10px;
            right: 10px;
        }

        }</style>

</head>
<body>
    <div class="layui-container main-content">
        <div class="layui-row page_header">
            <h1 class="main-title">中医经方推荐系统</h1>
            <div class="patient-info">
                <div class="patient-avatar">
                    <img src="~/images/user_icon.png" alt="" width="100%" height="100%">
                </div>
                <div>
                    <div class="status"><span>张一鸣</span><span>男</span><span>35岁</span></div>
                    <div class="admission_information"><p>住院号：<span>ZY20230512</span></p>丨<p>科室：<span>心血管内科</span></p></div>
                </div>
            </div>

        </div>



        <!-- 可折叠卡片容器 -->
        <div class="cards-container">
            <div class="layui-collapse card_group">
                <!-- 第一个卡片（默认展开） -->
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">桂枝汤 <span class="layui-badge layui-bg-red">推荐</span></h2>
                    <div class="layui-colla-content layui-show">
                        <div class="ai_group">
                            <div class="formula-title">由<span class="color1">中医知识库</span>生成</div>
                            <div class="formula-info">
                                <p><strong>组成：</strong>桂枝9g、芍药9g、甘草6g、生姜9g、大枣3枚</p>
                                <p><strong>功效：</strong>解肌发表，调和营卫</p>
                                <p><strong>主治：</strong>外感风寒表虚证。头痛发热，汗出恶风，鼻鸣干呕，苔白不渴，脉浮缓或浮弱。</p>
                                <p><strong>用法：</strong>水煎服，温覆取微汗。</p>
                                <p><strong>现代应用：</strong>感冒、流行性感冒、原因不明的低热、产后或病后低热等属外感风寒表虚证者。</p>
                            </div>
                        </div>
                        <div class="table_group">
                            <div class="formula-title">经方合计</div>
                            <table class="layui-hide" id="test1" lay-filter="test1"></table>
                        </div>
                        <div class="action-buttons">
                            <button class="layui-btn layui-btn-sm layui-bg-blue"><i class="layui-icon layui-icon-ok"></i>选择此方</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red"><i class="layui-icon layui-icon-close"></i>不采纳</button>
                        </div>
                    </div>
                </div>


                <!-- 第二个卡片（默认展开） -->
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">小柴胡汤 </h2>
                    <div class="layui-colla-content ">
                        <div class="ai_group">
                            <div class="formula-title">由<span class="color2">deepseek</span>生成</div>
                            <div class="formula-info">
                                <p><strong>组成：</strong>麻黄9g、桂枝6g、杏仁6g、甘草3g</p>
                                <p><strong>功效：</strong>发汗解表，宣肺平喘</p>
                                <p><strong>主治：</strong>外感风寒表实证。恶寒发热，头身疼痛，鼻塞流涕，口不渴，舌苔薄白，脉浮紧。</p>
                                <p><strong>用法：</strong>水煎服，温服取微汗。</p>
                                <p><strong>注意事项：</strong>表虚自汗、阴虚盗汗及温病等情况慎用。</p>
                            </div>
                        </div>
                        <!-- <div class="table_group">
                            <div class="formula-title">经方合计</div>
                            <table class="layui-hide" id="test2" lay-filter="test2"></table>
                        </div> -->
                        <div class="action-buttons">
                            <button class="layui-btn layui-btn-sm layui-bg-blue"><i class="layui-icon layui-icon-ok"></i>选择此方</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red"><i class="layui-icon layui-icon-close"></i>不采纳</button>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>



    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
           layui.use(['element', 'layer','jquery','table'], function(){
                var element = layui.element,
                    layer = layui.layer
                    $ = layui.jquery,
                    table = layui.table;





                // 初始化折叠面板
                element.render('collapse');

                function setContainerH(){
                    var winh = $(window).height();
                    var topH = $('.page_header').height();
                    var containerH = winh - topH-40;

                    $('.card_group').css('height', containerH);
                }
                setContainerH();

                $(window).resize(function(){
                    setContainerH();
                });

                var data = [
                    { id: 1, name: '桂枝', dosage: '9g    ', price: '30'},
                    { id: 2, name: '芍药', dosage: '9g    ', price: '30' },
                    { id: 3, name: '甘草', dosage: '6g   ', price: '8' },
                    { id: 4, name: '生姜', dosage: '9g   ', price: '0.1' },
                    { id: 5, name: '大枣', dosage: '3枚   ', price: '1' }
                ];

                 //展示已知数据
                table.render({
                    elem: '#test1'
                    ,title: '费用汇总'
                    ,height: '320' //设置表格高度
                    ,cols: [[ //标题栏
                         {field:'id', title:'ID', width:90,unresize: true, sort: true, totalRowText: '合计费用'}
                        ,{field: 'name', title: '药品', minWidth: 120}
                        ,{field: 'dosage', title: '计量', width: 150}
                        ,{field: 'price', title: '单价', width: 160
                        ,sort: true
                        ,totalRow: true
                        ,templet: function (d) {
                            return '￥' + d.price;
                        }}
                    ]]
                    ,data: data
                    ,page: true //是否显示分页
                    ,limits: [5, 7, 10]
                    ,limit: 5 //每页默认显示的数量
                    ,totalRow: true//开启合计行
                    ,response: {
                        statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
                        }
                    ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                        return {
                            "code": res.status, //解析接口状态
                            "msg": res.message, //解析提示文本
                            "count": res.total, //解析数据长度
                            "data": res.rows.item //解析数据列表
                        };
                    }
                });

                             //展示已知数据
                table.render({
                    elem: '#test2'
                    ,title: '费用汇总'
                    ,height: '300' //设置表格高度
                    ,cols: [[ //标题栏
                         {field:'id', title:'ID', width:90,unresize: true, sort: true, totalRowText: '合计费用'}
                        ,{field: 'name', title: '药品', minWidth: 120}
                        ,{field: 'dosage', title: '计量', width: 150}
                        ,{field: 'price', title: '单价', width: 160
                        ,sort: true
                        ,totalRow: true
                        ,templet: function (d) {
                            return '￥' + d.price;
                        }}
                    ]]
                    ,data: data
                    ,page: true //是否显示分页
                    ,limits: [5, 7, 10]
                    ,limit: 5 //每页默认显示的数量
                    ,totalRow: true//开启合计行
                    ,response: {
                        statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
                        }
                    ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                        return {
                            "code": res.status, //解析接口状态
                            "msg": res.message, //解析提示文本
                            "count": res.total, //解析数据长度
                            "data": res.rows.item //解析数据列表
                        };
                    }
                });
        });

    </script>
</body>
</html>