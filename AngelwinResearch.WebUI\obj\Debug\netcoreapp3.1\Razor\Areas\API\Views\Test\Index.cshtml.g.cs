#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "f1f834361a6506a24c876d112a85b49abcc7e7347f02826f3cb0a5288a8f799d"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_API_Views_Test_Index), @"mvc.1.0.view", @"/Areas/API/Views/Test/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"f1f834361a6506a24c876d112a85b49abcc7e7347f02826f3cb0a5288a8f799d", @"/Areas/API/Views/Test/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_API_Views_Test_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\Index.cshtml"
  
    ViewData["Title"] = "API测试页面";
    Layout = "~/Views/Shared/_Layout.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""container mt-4"">
    <div class=""row"">
        <div class=""col-12"">
            <h2>API功能测试</h2>
            <div class=""card"">
                <div class=""card-body"">
                    <h5 class=""card-title"">中医经方推荐API</h5>
                    <p class=""card-text"">测试中医经方推荐功能是否正常工作</p>
                    
                    <div class=""mb-3"">
                        <a href=""/API/TCMPrescription/Index"" class=""btn btn-primary"" target=""_blank"">
                            <i class=""fas fa-leaf""></i>
                            打开中医经方推荐页面
                        </a>
                        
                        <button id=""testApiBtn"" class=""btn btn-secondary ml-2"">
                            <i class=""fas fa-flask""></i>
                            测试API连接
                        </button>
                    </div>
                    
                    <div id=""testResult"" class=""mt-3"" style=""display: none;"">
                        <div class=""alert alert-info"">
                        ");
            WriteLiteral("    <h6>测试结果：</h6>\n                            <pre id=\"resultContent\"></pre>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        $(document).ready(function() {
            $('#testApiBtn').click(function() {
                $.get('/API/Test/TestTCM')
                    .done(function(response) {
                        $('#resultContent').text(JSON.stringify(response, null, 2));
                        $('#testResult').show();
                    })
                    .fail(function(xhr, status, error) {
                        $('#resultContent').text('测试失败: ' + error);
                        $('#testResult').show();
                    });
            });
        });
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
