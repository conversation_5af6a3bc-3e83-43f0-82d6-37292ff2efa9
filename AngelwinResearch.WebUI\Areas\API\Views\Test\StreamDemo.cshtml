@{
    ViewData["Title"] = "流式打字机效果演示";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>中医经方推荐 - 流式打字机效果演示</h2>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">测试流式输出效果</h5>
                    
                    <div class="mb-3">
                        <label for="symptomsInput" class="form-label">症状描述：</label>
                        <textarea id="symptomsInput" class="form-control" rows="3" 
                                  placeholder="请输入患者症状描述...">近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <button id="startStreamBtn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            开始流式输出演示
                        </button>
                        <button id="stopStreamBtn" class="btn btn-danger" disabled>
                            <i class="fas fa-stop"></i>
                            停止
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i>
                            清空
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>实时状态：</h6>
                            <div id="statusArea" class="border rounded p-3" style="height: 150px; overflow-y: auto; background-color: #f8f9fa;">
                                <p class="text-muted">等待开始...</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>流式内容预览：</h6>
                            <div id="streamPreview" class="border rounded p-3" style="height: 400px; overflow-y: auto; background-color: #fff;">
                                <p class="text-muted">内容将在这里实时显示...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/marked.min.js"></script>
    <script>
        let currentSource = null;
        let allData = "";
        let startTime = null;
        let charCount = 0;

        $(document).ready(function() {
            $('#startStreamBtn').click(function() {
                startStreamDemo();
            });

            $('#stopStreamBtn').click(function() {
                stopStream();
            });

            $('#clearBtn').click(function() {
                clearContent();
            });
        });

        function startStreamDemo() {
            var symptoms = $('#symptomsInput').val().trim();
            if (!symptoms) {
                alert('请输入症状描述');
                return;
            }

            // 重置状态
            allData = "";
            charCount = 0;
            startTime = new Date();
            
            // 更新UI状态
            $('#startStreamBtn').prop('disabled', true);
            $('#stopStreamBtn').prop('disabled', false);
            $('#statusArea').html('<p class="text-info">正在连接AI服务...</p>');
            $('#streamPreview').html('<p class="text-muted">等待数据流...</p>');

            // 创建EventSource连接
            currentSource = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' + encodeURIComponent(symptoms) + '&sessionId=');

            currentSource.onmessage = function (event) {
                var result = JSON.parse(event.data);
                var currentTime = new Date();
                var elapsed = (currentTime - startTime) / 1000;

                if (result.error) {
                    updateStatus('错误: ' + result.error, 'danger');
                    stopStream();
                    return;
                }

                if (result.type === 'chunk' && result.content) {
                    allData += result.content;
                    charCount += result.content.length;
                    
                    // 更新状态信息
                    updateStatus(`
                        <strong>流式接收中...</strong><br>
                        耗时: ${elapsed.toFixed(1)}秒<br>
                        字符数: ${charCount}<br>
                        最新片段: "${result.content}"
                    `, 'success');
                    
                    // 更新流式内容预览（打字机效果）
                    updateStreamPreview(allData);
                    
                } else if (result.type === 'complete' && result.output) {
                    // 完整输出
                    updateStatus(`
                        <strong>接收完成！</strong><br>
                        总耗时: ${elapsed.toFixed(1)}秒<br>
                        总字符数: ${result.output.length}
                    `, 'success');
                    
                    updateStreamPreview(result.output);
                    stopStream();
                }
            };

            currentSource.addEventListener('end', function (event) {
                stopStream();
            }, false);

            currentSource.onerror = function (event) {
                updateStatus('连接错误，请检查网络和服务器状态', 'danger');
                stopStream();
            };

            // 30秒超时
            setTimeout(function() {
                if (currentSource && currentSource.readyState !== EventSource.CLOSED) {
                    updateStatus('连接超时(30秒)', 'warning');
                    stopStream();
                }
            }, 30000);
        }

        function stopStream() {
            if (currentSource) {
                currentSource.close();
                currentSource = null;
            }
            
            $('#startStreamBtn').prop('disabled', false);
            $('#stopStreamBtn').prop('disabled', true);
        }

        function clearContent() {
            allData = "";
            charCount = 0;
            $('#statusArea').html('<p class="text-muted">等待开始...</p>');
            $('#streamPreview').html('<p class="text-muted">内容将在这里实时显示...</p>');
        }

        function updateStatus(message, type = 'info') {
            var alertClass = 'alert-' + type;
            $('#statusArea').html(`<div class="alert ${alertClass} mb-0">${message}</div>`);
            
            // 自动滚动到底部
            var statusArea = document.getElementById('statusArea');
            statusArea.scrollTop = statusArea.scrollHeight;
        }

        function updateStreamPreview(content) {
            try {
                // 使用marked解析markdown内容
                var htmlContent = marked.parse(content);
                $('#streamPreview').html(htmlContent);
                
                // 自动滚动到底部
                var previewArea = document.getElementById('streamPreview');
                previewArea.scrollTop = previewArea.scrollHeight;
                
            } catch (error) {
                console.error('解析markdown失败:', error);
                // 如果解析失败，显示原始内容
                $('#streamPreview').html('<pre>' + content + '</pre>');
            }
        }
    </script>
}
