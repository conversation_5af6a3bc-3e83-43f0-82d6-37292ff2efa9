# 中医经方推荐API

## 概述
这是一个基于AICO API的中医经方推荐系统，能够根据患者症状描述提供相应的中医经方推荐。

## 功能特性
- 流式响应：实时显示AI分析过程
- 症状分析：基于中医理论分析患者症状
- 经方推荐：提供具体的中医经方和用药建议
- 历史记录：保存推荐历史便于回顾

## API接口

### 1. 获取经方推荐
**接口地址：** `POST /API/TCMPrescription/GetPrescriptionRecommendation`

**参数：**
- `symptoms` (string, 必填): 症状描述
- `sessionId` (string, 可选): 会话ID，留空将自动生成

**响应格式：** Server-Sent Events (SSE)

**响应示例：**
```
data: {"content": "不能", "type": "chunk"}
data: {"content": "养", "type": "chunk"}
data: {"content": "神", "type": "chunk"}
data: {"output": "完整的分析结果...", "type": "complete", "sessionId": "1754297315"}
event: end
data: {"code": 100}
```

### 2. 获取推荐历史
**接口地址：** `GET /API/TCMPrescription/GetPrescriptionHistory`

**参数：**
- `sessionId` (string): 会话ID

**响应示例：**
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "symptoms": "失眠，面色暗黄，口干伴有耳鸣，健忘",
      "prescription": "酸枣仁汤",
      "createTime": "2024-01-01T10:00:00"
    }
  ]
}
```

## 使用方法

### 1. 直接访问页面
访问 `/API/TCMPrescription/Index` 使用Web界面进行症状输入和获取推荐。

### 2. API调用示例

#### JavaScript (Fetch API)
```javascript
const formData = new FormData();
formData.append('symptoms', '近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠');
formData.append('sessionId', '');

fetch('/API/TCMPrescription/GetPrescriptionRecommendation', {
    method: 'POST',
    body: formData
}).then(response => {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    function readStream() {
        reader.read().then(({ done, value }) => {
            if (done) return;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            lines.forEach(line => {
                if (line.startsWith('data: ')) {
                    const data = JSON.parse(line.substring(6));
                    console.log(data);
                }
            });
            
            readStream();
        });
    }
    
    readStream();
});
```

#### C# HttpClient
```csharp
using var client = new HttpClient();
var formData = new MultipartFormDataContent();
formData.Add(new StringContent("失眠，面色暗黄"), "symptoms");
formData.Add(new StringContent(""), "sessionId");

var response = await client.PostAsync("/API/TCMPrescription/GetPrescriptionRecommendation", formData);
var stream = await response.Content.ReadAsStreamAsync();
var reader = new StreamReader(stream);

while (!reader.EndOfStream)
{
    var line = await reader.ReadLineAsync();
    if (line?.StartsWith("data: ") == true)
    {
        var json = line.Substring(6);
        // 处理JSON数据
    }
}
```

## 测试
访问 `/API/Test/Index` 进行功能测试。

## 注意事项
1. 确保AICO API服务正常运行
2. 症状描述应尽可能详细和准确
3. 推荐结果仅供参考，不能替代专业医疗诊断
4. 建议在专业中医师指导下使用推荐的经方

## 技术栈
- ASP.NET Core 3.1
- Server-Sent Events (SSE)
- Bootstrap 4
- jQuery
- Font Awesome
- Toastr.js
