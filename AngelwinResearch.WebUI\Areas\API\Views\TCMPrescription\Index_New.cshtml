@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名老中医经方传承智能助手</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/js/marked.min.js"></script>
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .userwrap {
            padding: 10px 15px;
            background-color: rgb(239 246 255 / 0.7);
            border-bottom: 1px solid #ccc;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .row_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .userinfo {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .userinfo_val {
            padding: 0 5px;
        }

        .doc_check {
            color: black;
            font-weight: bold;
        }

        .prompt {
            margin: 5px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .operating {
            width: 100%;
            position: fixed;
            bottom: 0;
        }

        .input_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px;
            background-color: #fff;
            border: 1px solid #7a4d7b;
            border-radius: 6px;
            overflow: hidden;
        }

            .input_wrap .layui-icon {
                color: #7a4d7b;
                padding: 12px;
            }

            .input_wrap .layui-input {
                border: none;
                padding-left: 0;
            }

                .input_wrap .layui-input:hover {
                    border: none;
                }

        .layui-icon-release {
            background-color: #7a4d7b;
            color: #fff !important;
            cursor: pointer;
        }

        /*对话区*/
        .dialog_box {
            padding: 10px;
            overflow-y: auto;
            font-size: 16px;
        }

        .quizzer {
            padding: 10px 0;
            display: flex;
            justify-content: flex-end;
        }

        .text_wrap {
            width: 80%;
            background-color: rgba(255, 251, 235, 0.7);
            padding: 10px;
            border-radius: 6px 0 6px 6px;
        }

        .answer {
            width: 100%;
        }

        .answer_inner {
            min-height: 100px;
            padding: 10px;
            background-color: #fff;
            border-radius: 6px;
        }

        .prescription-content {
            font-family: "Microsoft YaHei", Arial, "Helvetica Neue", sans-serif !important;
            line-height: 1.6;
        }

        .prescription-section {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }

        .prescription-title {
            font-weight: bold;
            color: #2c5530;
            margin-bottom: 8px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="chat layui-form">
        <div class="userwrap row_wrap">
            <div class="userinfo">
                <div style="width:20px;"></div>
                <div class="userinfo_val doc_check">
                    <i class="layui-icon layui-icon-triangle-r" style="font-size: 20px; color: green;"></i>
                    中医经方推荐系统 - 请描述患者症状
                </div>
            </div>
        </div>
        <div class="operating layui-form">
            <div class="prompt">
                <div>你可以和我说：</div>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠')">失眠症状</button>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('胸闷气短，心悸怔忡，舌淡苔白，脉细弱')">心悸症状</button>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="setInput('头痛眩晕，耳鸣如蝉，腰膝酸软，舌红少苔')">头痛眩晕</button>
            </div>
            <div class="input_wrap">
                <div class="layui-input-split layui-input-prefix">
                    <i class="layui-icon layui-icon-dialogue"></i>
                </div>
                <input type="text" placeholder="请输入患者症状描述..." class="layui-input" id="Input">
                <i class="layui-icon layui-icon-release" id="InputBtn"></i>
            </div>
        </div>

        <div class="dialog_box" id="dialog_box">
            <!-- 对话内容将在这里动态生成 -->
        </div>
    </div>

    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
        layui.use(['jquery', 'layer', 'form'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form;

            var xuhao = 1;
            var source = null;

            $(window).resize(function () {
                setDialogBoxH();
            });

            $(document).ready(function () {
                setDialogBoxH();
            });

            function setDialogBoxH() {
                var winH = $(window).height();
                var userwrapH = $(".userwrap").height();
                var operatingH = $(".operating").height();
                var DialogBox = winH - (userwrapH + operatingH) - 40;
                $(".dialog_box").css("height", DialogBox + "px");
            }

            let input_p = document.getElementById("Input");

            document.onkeydown = function () {
                if (event.keyCode == 13) {
                    GetPrescriptionRecommendation();
                }
            }

            // 设置输入框内容
            window.setInput = function(text) {
                $("#Input").val(text);
            }

            function GetPrescriptionRecommendation() {
                if ($.trim($("#Input").val()) == "") {
                    layer.msg("请输入症状描述！");
                    return;
                }

                var indexs = layer.load();
                var htm = `<div class="quizzer"><div class="text_wrap" id="answer">`;
                htm += `<p>` + $("#Input").val() + `</p></div></div>`;
                $(".dialog_box").append(htm);

                var symptoms = $("#Input").val();
                $("#Input").val("");

                // 关闭之前的连接
                if (source) {
                    source.close();
                }

                // 创建SSE连接
                source = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' + encodeURIComponent(symptoms) + '&sessionId=');
                var allData = "";
                var i = 0;
                var elementId = "A" + xuhao;

                source.onmessage = function (event) {
                    layer.close(indexs);
                    var result = JSON.parse(event.data);

                    if (result.error) {
                        layer.msg(result.error);
                        source.close();
                        return;
                    }

                    if (result.type === 'chunk' && result.content) {
                        // 流式内容
                        if (i == 0) {
                            var htm1Result = `<div class='answer'><div class='answer_inner'>
                                                <pre class='prescription-content' id='A` + xuhao + `'></pre>
                                              </div></div>`;
                            $(".dialog_box").append(htm1Result);
                        }

                        var btnloading = document.getElementById(elementId);
                        allData = allData + result.content;
                        var htmlContent = marked.parse(allData);
                        btnloading.innerHTML = htmlContent;
                        i = i + 1;
                    } else if (result.type === 'complete' && result.output) {
                        // 完整输出
                        var btnloading = document.getElementById(elementId);
                        var htmlContent = marked.parse(result.output);
                        btnloading.innerHTML = htmlContent;
                    } else if (result.code === 100) {
                        // 结束标志
                        // 可以在这里添加结束处理逻辑
                    }

                    resetHistoryscrollTop();
                };

                source.addEventListener('end', function (event) {
                    xuhao = xuhao + 1;
                    var result = JSON.parse(event.data);
                    if (result.code == 100) {
                        // 处理结束事件
                    } else {
                        layer.msg(result.msg);
                    }
                    source.close();
                }, false);

                source.onerror = function (event) {
                    layer.close(indexs);
                    xuhao = xuhao + 1;
                    layer.msg("连接出现错误，请重试");
                    source.close();
                };
            }

            $("#InputBtn").on("click", function () {
                GetPrescriptionRecommendation();
            });

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById("dialog_box");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 100);
                }
            }
        });
    </script>
</body>
</html>
