@{
    ViewData["Title"] = "患者信息流程演示";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>中医经方推荐 - 完整流程演示</h2>
            <p class="text-muted">演示从患者信息录入到经方推荐的完整流程</p>

            <!-- 流程步骤 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">流程步骤</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="btn btn-primary btn-circle mb-2">1</div>
                                <p>患者信息录入</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="btn btn-secondary btn-circle mb-2">2</div>
                                <p>创建会话</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="btn btn-secondary btn-circle mb-2">3</div>
                                <p>AI分析症状</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="btn btn-secondary btn-circle mb-2">4</div>
                                <p>经方推荐</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 方案对比 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">❌ 旧方案 - URL参数传递</h5>
                        </div>
                        <div class="card-body">
                            <h6>问题：</h6>
                            <ul>
                                <li>URL过长，不美观</li>
                                <li>参数暴露在地址栏，不安全</li>
                                <li>浏览器URL长度限制</li>
                                <li>中文参数编码问题</li>
                            </ul>

                            <h6>示例URL：</h6>
                            <div class="alert alert-warning">
                                <small>
                                    /API/TCMPrescription/Index?name=张一鸣&gender=男&age=35&symptoms=近日失眠，面色暗黄，口干伴有耳鸣，健忘...
                                </small>
                            </div>

                            <button class="btn btn-outline-danger" onclick="testOldMethod()">
                                测试旧方案
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">✅ 新方案 - Session会话</h5>
                        </div>
                        <div class="card-body">
                            <h6>优势：</h6>
                            <ul>
                                <li>URL简洁，只传递sessionId</li>
                                <li>患者信息安全存储在服务器</li>
                                <li>支持大量数据传递</li>
                                <li>更好的用户体验</li>
                            </ul>

                            <h6>示例URL：</h6>
                            <div class="alert alert-success">
                                <small>
                                    /API/TCMPrescription/Index?sessionId=abc123
                                </small>
                            </div>

                            <button class="btn btn-outline-success" onclick="testNewMethod()">
                                测试新方案
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速测试区域 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">快速测试</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>1. 患者信息录入页面</h6>
                            <p class="text-muted">完整的患者信息录入表单</p>
                            <a href="/API/TCMPrescription/PatientInfo" class="btn btn-primary" target="_blank">
                                <i class="fas fa-user-plus"></i>
                                打开录入页面
                            </a>
                        </div>

                        <div class="col-md-4">
                            <h6>2. 直接测试推荐</h6>
                            <p class="text-muted">使用默认患者信息测试</p>
                            <button class="btn btn-success" onclick="createTestSession()">
                                <i class="fas fa-play"></i>
                                创建测试会话
                            </button>
                        </div>

                        <div class="col-md-4">
                            <h6>3. HTML实体解码测试</h6>
                            <p class="text-muted">测试HTML实体编码解码</p>
                            <a href="/API/Test/DecodeTest" class="btn btn-warning" target="_blank">
                                <i class="fas fa-code"></i>
                                解码测试页面
                            </a>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <h6>4. API接口测试</h6>
                            <p class="text-muted">测试各个API接口</p>
                            <a href="/API/Test/StreamDemo" class="btn btn-info" target="_blank">
                                <i class="fas fa-flask"></i>
                                API测试页面
                            </a>
                        </div>

                        <div class="col-md-4">
                            <h6>5. HTML编码症状测试</h6>
                            <p class="text-muted">测试HTML实体编码的症状</p>
                            <button class="btn btn-danger" onclick="testEncodedSymptoms()">
                                <i class="fas fa-bug"></i>
                                测试编码症状
                            </button>
                        </div>

                        <div class="col-md-4">
                            <h6>6. 完整流程测试</h6>
                            <p class="text-muted">测试所有功能</p>
                            <button class="btn btn-dark" onclick="runFullTest()">
                                <i class="fas fa-play-circle"></i>
                                完整测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试结果显示 -->
            <div id="testResult" class="card mt-4" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">测试结果</h5>
                </div>
                <div class="card-body">
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .btn-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
}

@section Scripts {
    <script>
        function testOldMethod() {
            var symptoms = encodeURIComponent('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠');
            var url = `/API/TCMPrescription/Index?symptoms=${symptoms}&name=张一鸣&gender=男&age=35&department=心血管内科`;

            showResult('旧方案测试', `
                <div class="alert alert-warning">
                    <h6>生成的URL：</h6>
                    <p style="word-break: break-all;">${url}</p>
                    <p><strong>URL长度：</strong>${url.length} 字符</p>
                </div>
                <button class="btn btn-primary" onclick="window.open('${url}', '_blank')">
                    在新窗口中打开
                </button>
            `);
        }

        function testNewMethod() {
            createTestSession();
        }

        function createTestSession() {
            var patientData = {
                Name: '张一鸣',
                Gender: '男',
                Age: 35,
                HospitalNumber: 'ZY20230512',
                Department: '心血管内科',
                Doctor: '李医生',
                Symptoms: '近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠',
                Diagnosis: '失眠'
            };

            showResult('新方案测试', `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i>
                    正在创建患者会话...
                </div>
            `);

            $.ajax({
                url: '/API/TCMPrescription/CreatePatientSession',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(patientData),
                success: function(response) {
                    if (response.success) {
                        showResult('新方案测试', `
                            <div class="alert alert-success">
                                <h6>✅ 会话创建成功！</h6>
                                <p><strong>会话ID：</strong>${response.sessionId}</p>
                                <p><strong>简洁URL：</strong>${response.redirectUrl}</p>
                                <p><strong>URL长度：</strong>${response.redirectUrl.length} 字符</p>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-success" onclick="window.open('${response.redirectUrl}', '_blank')">
                                        <i class="fas fa-external-link-alt"></i>
                                        打开推荐页面
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-info" onclick="testGetPatientInfo('${response.sessionId}')">
                                        <i class="fas fa-info-circle"></i>
                                        测试获取患者信息
                                    </button>
                                </div>
                            </div>
                        `);
                    } else {
                        showResult('新方案测试', `
                            <div class="alert alert-danger">
                                <h6>❌ 创建会话失败</h6>
                                <p>${response.message}</p>
                            </div>
                        `);
                    }
                },
                error: function() {
                    showResult('新方案测试', `
                        <div class="alert alert-danger">
                            <h6>❌ 网络错误</h6>
                            <p>无法连接到服务器</p>
                        </div>
                    `);
                }
            });
        }

        function testGetPatientInfo(sessionId) {
            $.get('/API/TCMPrescription/GetPatientInfo', { sessionId: sessionId })
                .done(function(response) {
                    if (response.success) {
                        var patient = response.data;
                        showResult('患者信息获取测试', `
                            <div class="alert alert-success">
                                <h6>✅ 成功获取患者信息</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>姓名：</strong></td><td>${patient.Name}</td></tr>
                                    <tr><td><strong>性别：</strong></td><td>${patient.Gender}</td></tr>
                                    <tr><td><strong>年龄：</strong></td><td>${patient.Age}岁</td></tr>
                                    <tr><td><strong>住院号：</strong></td><td>${patient.HospitalNumber}</td></tr>
                                    <tr><td><strong>科室：</strong></td><td>${patient.Department}</td></tr>
                                    <tr><td><strong>症状：</strong></td><td>${patient.Symptoms}</td></tr>
                                    <tr><td><strong>诊断：</strong></td><td>${patient.Diagnosis}</td></tr>
                                </table>
                            </div>
                        `);
                    } else {
                        showResult('患者信息获取测试', `
                            <div class="alert alert-danger">
                                <h6>❌ 获取失败</h6>
                                <p>${response.message}</p>
                            </div>
                        `);
                    }
                })
                .fail(function() {
                    showResult('患者信息获取测试', `
                        <div class="alert alert-danger">
                            <h6>❌ 网络错误</h6>
                            <p>无法获取患者信息</p>
                        </div>
                    `);
                });
        }

        function testEncodedSymptoms() {
            var encodedSymptoms = '&#x8FD1;&#x65E5;&#x5931;&#x7720;&#xFF0C;&#x9762;&#x8272;&#x6697;&#x9EC4;&#xFF0C;&#x53E3;&#x5E72;&#x4F34;&#x6709;&#x8033;&#x9E23;&#xFF0C;&#x5065;&#x5FD8;';
            var url = `/API/TCMPrescription/Index?symptoms=${encodedSymptoms}`;

            showResult('HTML实体编码症状测试', `
                <div class="alert alert-warning">
                    <h6>测试HTML实体编码的症状：</h6>
                    <p><strong>编码症状：</strong>${encodedSymptoms}</p>
                    <p><strong>应该解码为：</strong>近日失眠，面色暗黄，口干伴有耳鸣，健忘</p>
                    <p><strong>测试URL：</strong>${url}</p>
                </div>
                <button class="btn btn-primary" onclick="window.open('${url}', '_blank')">
                    在新窗口中测试
                </button>
                <a href="/API/Test/DecodeTest" class="btn btn-info ms-2" target="_blank">
                    打开解码测试工具
                </a>
            `);
        }

        function runFullTest() {
            showResult('完整流程测试', `
                <div class="alert alert-info">
                    <h6>正在执行完整流程测试...</h6>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                    </div>
                    <div id="testSteps"></div>
                </div>
            `);

            var steps = [
                { name: '创建患者会话', action: () => createTestSession() },
                { name: '测试HTML实体解码', action: () => testEncodedSymptoms() },
                { name: '测试API连接', action: () => window.open('/API/Test/StreamDemo', '_blank') },
                { name: '打开患者信息录入', action: () => window.open('/API/TCMPrescription/PatientInfo', '_blank') }
            ];

            var currentStep = 0;
            var progressBar = $('.progress-bar');
            var stepsDiv = $('#testSteps');

            function executeNextStep() {
                if (currentStep < steps.length) {
                    var step = steps[currentStep];
                    var progress = ((currentStep + 1) / steps.length) * 100;

                    progressBar.css('width', progress + '%');
                    stepsDiv.append(`<p>✅ ${step.name}</p>`);

                    setTimeout(() => {
                        step.action();
                        currentStep++;
                        executeNextStep();
                    }, 1000);
                } else {
                    stepsDiv.append(`<p class="text-success"><strong>✅ 所有测试完成！</strong></p>`);
                }
            }

            executeNextStep();
        }

        function showResult(title, content) {
            $('#testResult').show();
            $('#resultContent').html(`
                <h6>${title}</h6>
                ${content}
            `);

            // 滚动到结果区域
            $('html, body').animate({
                scrollTop: $('#testResult').offset().top
            }, 500);
        }
    </script>
}
