#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\PatientInfo.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "624f65d1d406929726d5608eeedcddf139a75a8b7a6e7961a27ab3c9434b57e3"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_API_Views_TCMPrescription_PatientInfo), @"mvc.1.0.view", @"/Areas/API/Views/TCMPrescription/PatientInfo.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"624f65d1d406929726d5608eeedcddf139a75a8b7a6e7961a27ab3c9434b57e3", @"/Areas/API/Views/TCMPrescription/PatientInfo.cshtml")]
    #nullable restore
    internal sealed class Areas_API_Views_TCMPrescription_PatientInfo : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\PatientInfo.cshtml"
  
    ViewData["Title"] = "患者信息录入";
    Layout = "~/Views/Shared/_Layout.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""container mt-4"">
    <div class=""row justify-content-center"">
        <div class=""col-md-8"">
            <div class=""card"">
                <div class=""card-header bg-primary text-white"">
                    <h4 class=""mb-0"">
                        <i class=""fas fa-user-md""></i>
                        患者信息录入 - 中医经方推荐系统
                    </h4>
                </div>
                <div class=""card-body"">
                    <form id=""patientForm"">
                        <div class=""row"">
                            <div class=""col-md-6"">
                                <div class=""form-group mb-3"">
                                    <label for=""patientName"" class=""form-label"">患者姓名 <span class=""text-danger"">*</span></label>
                                    <input type=""text"" class=""form-control"" id=""patientName"" name=""Name"" value=""张一鸣"" required>
                                </div>
                            </div>
                            <div class=""col-md-3"">
                    ");
            WriteLiteral(@"            <div class=""form-group mb-3"">
                                    <label for=""patientGender"" class=""form-label"">性别 <span class=""text-danger"">*</span></label>
                                    <select class=""form-control"" id=""patientGender"" name=""Gender"" required>
                                        <option value=""男"" selected>男</option>
                                        <option value=""女"">女</option>
                                    </select>
                                </div>
                            </div>
                            <div class=""col-md-3"">
                                <div class=""form-group mb-3"">
                                    <label for=""patientAge"" class=""form-label"">年龄 <span class=""text-danger"">*</span></label>
                                    <input type=""number"" class=""form-control"" id=""patientAge"" name=""Age"" value=""35"" min=""1"" max=""120"" required>
                                </div>
                            </div>
                       ");
            WriteLiteral(@" </div>

                        <div class=""row"">
                            <div class=""col-md-6"">
                                <div class=""form-group mb-3"">
                                    <label for=""hospitalNumber"" class=""form-label"">住院号</label>
                                    <input type=""text"" class=""form-control"" id=""hospitalNumber"" name=""HospitalNumber"" value=""ZY20230512"">
                                </div>
                            </div>
                            <div class=""col-md-6"">
                                <div class=""form-group mb-3"">
                                    <label for=""department"" class=""form-label"">科室</label>
                                    <input type=""text"" class=""form-control"" id=""department"" name=""Department"" value=""心血管内科"">
                                </div>
                            </div>
                        </div>

                        <div class=""form-group mb-3"">
                            <label for=""doctor"" class=""form-label");
            WriteLiteral(@""">主治医师</label>
                            <input type=""text"" class=""form-control"" id=""doctor"" name=""Doctor"" placeholder=""请输入主治医师姓名"">
                        </div>

                        <div class=""form-group mb-3"">
                            <label for=""symptoms"" class=""form-label"">症状描述 <span class=""text-danger"">*</span></label>
                            <textarea class=""form-control"" id=""symptoms"" name=""Symptoms"" rows=""4"" required 
                                      placeholder=""请详细描述患者症状..."">近日失眠，面色暗黄，口干伴有耳鸣，健忘</textarea>
                            <small class=""form-text text-muted"">请尽可能详细地描述患者的症状表现</small>
                        </div>

                        <div class=""form-group mb-3"">
                            <label for=""diagnosis"" class=""form-label"">初步诊断</label>
                            <input type=""text"" class=""form-control"" id=""diagnosis"" name=""Diagnosis"" value=""失眠"" 
                                   placeholder=""请输入初步诊断结果"">
                        </div>

                     ");
            WriteLiteral(@"   <div class=""form-group mb-4"">
                            <h6>快速症状模板：</h6>
                            <div class=""btn-group-vertical w-100"" role=""group"">
                                <button type=""button"" class=""btn btn-outline-secondary btn-sm mb-1"" 
                                        onclick=""setSymptoms('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠', '失眠')"">
                                    失眠症状模板
                                </button>
                                <button type=""button"" class=""btn btn-outline-secondary btn-sm mb-1"" 
                                        onclick=""setSymptoms('胸闷气短，心悸怔忡，舌淡苔白，脉细弱', '心悸')"">
                                    心悸症状模板
                                </button>
                                <button type=""button"" class=""btn btn-outline-secondary btn-sm mb-1"" 
                                        onclick=""setSymptoms('头痛眩晕，耳鸣如蝉，腰膝酸软，舌红少苔', '头痛眩晕')"">
                                    头痛眩晕模板
                                </button>
                       ");
            WriteLiteral(@"         <button type=""button"" class=""btn btn-outline-secondary btn-sm mb-1"" 
                                        onclick=""setSymptoms('咳嗽痰多，胸闷气喘，舌苔白腻，脉滑', '咳嗽')"">
                                    咳嗽痰多模板
                                </button>
                            </div>
                        </div>

                        <div class=""text-center"">
                            <button type=""submit"" class=""btn btn-primary btn-lg"">
                                <i class=""fas fa-search""></i>
                                开始经方推荐分析
                            </button>
                            <button type=""reset"" class=""btn btn-secondary btn-lg ms-2"">
                                <i class=""fas fa-redo""></i>
                                重置表单
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class=""card mt-3"">
                <div class=""card-body"">
    ");
            WriteLiteral(@"                <h6 class=""card-title"">使用说明：</h6>
                    <ul class=""mb-0"">
                        <li>请填写完整的患者基本信息</li>
                        <li>症状描述越详细，AI推荐的经方越准确</li>
                        <li>可以使用快速模板，也可以自定义症状描述</li>
                        <li>点击""开始经方推荐分析""后将跳转到推荐页面</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        function setSymptoms(symptoms, diagnosis) {
            $('#symptoms').val(symptoms);
            $('#diagnosis').val(diagnosis);
        }

        $(document).ready(function() {
            $('#patientForm').on('submit', function(e) {
                e.preventDefault();
                
                // 验证必填字段
                if (!$('#patientName').val().trim()) {
                    alert('请输入患者姓名');
                    $('#patientName').focus();
                    return;
                }
                
                if (!$('#symptoms').val().trim()) {
                    alert('请输入症状描述');
                    $('#symptoms').focus();
                    return;
                }

                // 收集表单数据
                var formData = {
                    Name: $('#patientName').val().trim(),
                    Gender: $('#patientGender').val(),
                    Age: parseInt($('#patientAge').val()),
                    HospitalNumber: $('#hospitalNumber').val().trim(),
");
                WriteLiteral(@"                    Department: $('#department').val().trim(),
                    Doctor: $('#doctor').val().trim(),
                    Symptoms: $('#symptoms').val().trim(),
                    Diagnosis: $('#diagnosis').val().trim()
                };

                // 显示加载状态
                var $submitBtn = $('button[type=""submit""]');
                var originalText = $submitBtn.html();
                $submitBtn.prop('disabled', true).html('<i class=""fas fa-spinner fa-spin""></i> 正在处理...');

                // 提交到服务器创建会话
                $.ajax({
                    url: '/API/TCMPrescription/CreatePatientSession',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        if (response.success) {
                            // 跳转到推荐页面
                            window.location.href = response.redirectUrl;
                        } else {
            ");
                WriteLiteral(@"                alert('创建会话失败: ' + response.message);
                            $submitBtn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function() {
                        alert('网络错误，请重试');
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // 重置表单
            $('#patientForm').on('reset', function() {
                setTimeout(function() {
                    $('#patientName').val('张一鸣');
                    $('#patientGender').val('男');
                    $('#patientAge').val('35');
                    $('#hospitalNumber').val('ZY20230512');
                    $('#department').val('心血管内科');
                    $('#symptoms').val('近日失眠，面色暗黄，口干伴有耳鸣，健忘');
                    $('#diagnosis').val('失眠');
                }, 10);
            });
        });
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
