using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using Common.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Areas.API.Controllers
{
    [Area("API")]
    public class TCMPrescriptionController : Controller
    {
        private IConfiguration Configuration { get; }
        private readonly AngelwinResearchDbContext db;

        public TCMPrescriptionController(IConfiguration configuration, AngelwinResearchDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }

        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 中医经方推荐API
        /// </summary>
        /// <param name="symptoms">症状描述</param>
        /// <param name="sessionId">会话ID</param>
        /// <returns></returns>
        [HttpPost]
        [SSE]
        public async Task<IActionResult> GetPrescriptionRecommendation(string symptoms, string sessionId = "")
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            response.ContentType = "text/event-stream";

            if (string.IsNullOrWhiteSpace(symptoms))
            {
                await response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = "症状描述不能为空", code = -100 })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }

            string apiUrl = "https://aiworker.aminer.cn/aicoapi/gateway/v2/chatbot/api_run/1754016307_9f6ba609-8b62-4daf-9347-b53731edcc4e";

            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);

            var requestBody = new
            {
                doc_list = new string[] { },
                image_url = "",
                query = symptoms,
                session_id = sessionId,
                stream = true
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = new StringContent(json, Encoding.UTF8, "application/json")
            };

            try
            {
                using var apiResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                if (apiResponse.IsSuccessStatusCode)
                {
                    var stream = await apiResponse.Content.ReadAsStreamAsync();
                    var streamReader = new StreamReader(stream, Encoding.UTF8);

                    string currentEvent = "";
                    while (!streamReader.EndOfStream)
                    {
                        var line = await streamReader.ReadLineAsync();
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            // 处理event行
                            if (line.StartsWith("event:"))
                            {
                                currentEvent = line.Substring(6).Trim();
                                if (currentEvent == "FINISH")
                                {
                                    await response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { code = 100 })}\n\n");
                                    await response.Body.FlushAsync();
                                    break;
                                }
                            }
                            // 处理data行
                            else if (line.StartsWith("data:"))
                            {
                                var dataContent = line.Substring(5);
                                try
                                {
                                    var jsonData = JsonConvert.DeserializeObject<TCMMessage>(dataContent);
                                    
                                    // 检查是否是llm_chunk事件，包含content
                                    if (jsonData?.@event == "llm_chunk" && jsonData?.data?.choices != null && jsonData.data.choices.Count > 0)
                                    {
                                        var content = jsonData.data.choices[0]?.delta?.content;
                                        if (!string.IsNullOrEmpty(content))
                                        {
                                            // 发送提取的content内容
                                            await response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = content, type = "chunk" })}\n\n");
                                            await response.Body.FlushAsync();
                                        }
                                    }
                                    // 检查是否是message_finished事件，包含完整输出
                                    else if (jsonData?.@event == "message_finished" && !string.IsNullOrEmpty(jsonData?.data?.output))
                                    {
                                        string output = jsonData.data.output;
                                        await response.WriteAsync($"data: {JsonConvert.SerializeObject(new { output = output, type = "complete", sessionId = jsonData.session_id })}\n\n");
                                        await response.Body.FlushAsync();
                                    }
                                }
                                catch (JsonException ex)
                                {
                                    LoggerHelper.WriteInfo("其他日志", $"TCM API JSON解析异常: {ex.Message}, 原始数据: {line}");
                                }
                            }
                        }
                    }
                }
                else
                {
                    await response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = "API调用失败", code = -100 })}\n\n");
                    await response.Body.FlushAsync();
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"TCM API调用异常: {ex.Message}");
                await response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = ex.Message, code = -100 })}\n\n");
                await response.Body.FlushAsync();
            }

            return new EmptyResult();
        }

        /// <summary>
        /// 获取经方推荐历史记录
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPrescriptionHistory(string sessionId)
        {
            try
            {
                // 这里可以从数据库获取历史记录
                // 暂时返回示例数据
                var history = new List<object>
                {
                    new { 
                        id = 1, 
                        symptoms = "失眠，面色暗黄，口干伴有耳鸣，健忘", 
                        prescription = "酸枣仁汤", 
                        createTime = DateTime.Now.AddDays(-1) 
                    }
                };

                return Json(new { code = 0, data = history });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"获取经方推荐历史异常: {ex.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }
        }

        #region TCM Models
        public class TCMMessage
        {
            public string @event { get; set; }
            public string session_id { get; set; }
            public string step_id { get; set; }
            public long created { get; set; }
            public TCMData data { get; set; }
            public List<string> url_list { get; set; }
            public object search_results { get; set; }
            public string request_id { get; set; }
            public object usage { get; set; }
            public string model_stop_reason { get; set; }
        }

        public class TCMData
        {
            public string id { get; set; }
            public long created { get; set; }
            public string model { get; set; }
            public List<TCMChoice> choices { get; set; }
            public string output { get; set; }
        }

        public class TCMChoice
        {
            public int index { get; set; }
            public TCMDelta delta { get; set; }
        }

        public class TCMDelta
        {
            public string role { get; set; }
            public string content { get; set; }
        }
        #endregion
    }
}
