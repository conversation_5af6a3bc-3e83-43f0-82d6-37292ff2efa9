﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>915f868d-e43b-455a-bc93-300bf1ca8b37</UserSecretsId>
  </PropertyGroup>





  <ItemGroup>
    <Compile Remove="wwwroot\dist\**" />
    <Content Remove="wwwroot\dist\**" />
    <EmbeddedResource Remove="wwwroot\dist\**" />
    <None Remove="wwwroot\dist\**" />
  </ItemGroup>





  <ItemGroup>
    <Compile Remove="Areas\PatientDiscoveryManage\Controllers\ResearchDataDetailsController - 副本.cs" />
    <Compile Remove="Extensions\HttpContextJob.cs" />
    <Compile Remove="Extensions\IOCJobFactory.cs" />
    <Compile Remove="Extensions\QuartzNETExtension.cs" />
    <Compile Remove="Extensions\StaticHttpContextExtensions.cs" />
    <Compile Remove="Extensions\WikiJob.cs" />
    <Compile Remove="Unity\ApiHelper.cs" />
    <Compile Remove="Unity\LoggerHelper.cs" />
  </ItemGroup>





  <ItemGroup>
    <Content Remove="Areas\PatientDiscoveryManage\Views\ResearchDataDetails\Index - 副本.cshtml" />
  </ItemGroup>





  <ItemGroup>
    <None Include="Areas\BasicConfig\Views\HospitalDepts\Index.cshtml" />
    <None Include="Views\Log\LoginLog.cshtml" />
    <None Include="Views\Log\Logs.cshtml" />
    <None Include="Views\Menu\Index.cshtml" />
    <None Include="Views\Role\Index.cshtml" />
    <None Include="Views\User\Index.cshtml" />
    <None Include="wwwroot\lib\CodeMirror\lib\codemirror.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\apl\apl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\asciiarmor\asciiarmor.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\asn.1\asn.1.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\asterisk\asterisk.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\brainfuck\brainfuck.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\clike\clike.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\clike\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\clojure\clojure.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\cmake\cmake.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\cobol\cobol.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\coffeescript\coffeescript.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\commonlisp\commonlisp.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\crystal\crystal.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\css\css.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\css\gss_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\css\less_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\css\scss_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\css\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\cypher\cypher.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\dart\dart.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\diff\diff.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\django\django.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\dockerfile\dockerfile.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\dtd\dtd.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\dylan\dylan.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\dylan\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\d\d.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ebnf\ebnf.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ecl\ecl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\eiffel\eiffel.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\elm\elm.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\erlang\erlang.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\factor\factor.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\fcl\fcl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\forth\forth.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\fortran\fortran.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\gas\gas.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\gfm\gfm.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\gfm\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\gherkin\gherkin.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\go\go.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\groovy\groovy.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\haml\haml.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\haml\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\handlebars\handlebars.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\haskell-literate\haskell-literate.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\haskell\haskell.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\haxe\haxe.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\htmlembedded\htmlembedded.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\htmlmixed\htmlmixed.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\http\http.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\idl\idl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\javascript\javascript.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\javascript\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\jinja2\jinja2.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\jsx\jsx.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\jsx\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\julia\julia.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\livescript\livescript.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\lua\lua.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\markdown\markdown.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\markdown\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mathematica\mathematica.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mbox\mbox.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\meta.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mirc\mirc.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mllike\mllike.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\modelica\modelica.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mscgen\mscgen.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mscgen\mscgen_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mscgen\msgenny_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mscgen\xu_test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\mumps\mumps.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\nginx\nginx.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\nsis\nsis.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ntriples\ntriples.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\octave\octave.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\oz\oz.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\pascal\pascal.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\pegjs\pegjs.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\perl\perl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\php\php.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\php\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\pig\pig.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\powershell\powershell.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\powershell\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\properties\properties.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\protobuf\protobuf.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\pug\pug.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\puppet\puppet.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\python\python.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\python\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\q\q.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\rpm\rpm.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\rst\rst.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ruby\ruby.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ruby\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\rust\rust.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\rust\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\r\r.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\sass\sass.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\sas\sas.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\scheme\scheme.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\shell\shell.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\shell\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\sieve\sieve.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\slim\slim.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\slim\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\smalltalk\smalltalk.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\smarty\smarty.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\solr\solr.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\soy\soy.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\sparql\sparql.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\spreadsheet\spreadsheet.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\sql\sql.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\stex\stex.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\stex\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\stylus\stylus.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\swift\swift.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\tcl\tcl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\textile\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\textile\textile.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\tiddlywiki\tiddlywiki.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\tiki\tiki.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\toml\toml.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\tornado\tornado.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\troff\troff.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ttcn-cfg\ttcn-cfg.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\ttcn\ttcn.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\turtle\turtle.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\twig\twig.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\vbscript\vbscript.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\vb\vb.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\velocity\velocity.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\verilog\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\verilog\verilog.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\vhdl\vhdl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\vue\vue.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\webidl\webidl.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\xml\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\xml\xml.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\xquery\test.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\xquery\xquery.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\yacas\yacas.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\yaml-frontmatter\yaml-frontmatter.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\yaml\yaml.js" />
    <None Include="wwwroot\lib\CodeMirror\mode\z80\z80.js" />
    <None Include="wwwroot\lib\perfect-scrollbar\perfect-scrollbar.min.js" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="4.5.3.3" />
    <PackageReference Include="FluentFTP" Version="52.1.0" />
    <PackageReference Include="Html2Markdown" Version="3.2.3.392" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.32" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.10" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="3.1.5" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.6.2" />
    <PackageReference Include="Microsoft.SemanticKernel.Core" Version="1.6.2" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PDFtoImage" Version="4.1.0" />
    <PackageReference Include="Quartz" Version="3.8.1" />
    <PackageReference Include="SharpToken" Version="2.0.3" />
 
    <PackageReference Include="Spire.PDF" Version="10.5.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AngelwinResearch.Library\AngelwinResearch.Library.csproj" />
    <ProjectReference Include="..\Common.QuartzNet\Common.QuartzNet.csproj" />
    <ProjectReference Include="..\Common.Tools\Common.Tools.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\BasicConfig\Data\" />
    <Folder Include="Areas\BasicConfig\Models\" />
    <Folder Include="Areas\HospitalCRF\Controllers\" />
    <Folder Include="Areas\HospitalCRF\Views\" />
    <Folder Include="Areas\ReportingManage\Models\" />
    <Folder Include="Areas\TaskManage\Models\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Sap.Data.Hana.Core.v2.1">
      <HintPath>..\package\sapHana\vcore2.1\Sap.Data.Hana.Core.v2.1.dll</HintPath>
    </Reference>
  </ItemGroup>



</Project>
