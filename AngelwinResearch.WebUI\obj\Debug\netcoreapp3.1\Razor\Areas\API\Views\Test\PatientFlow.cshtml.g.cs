#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\PatientFlow.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2c9081e87239c1bf06b0c6f8fcfa2b8a4c671cce709a3c28149218740283b1a6"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_API_Views_Test_PatientFlow), @"mvc.1.0.view", @"/Areas/API/Views/Test/PatientFlow.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2c9081e87239c1bf06b0c6f8fcfa2b8a4c671cce709a3c28149218740283b1a6", @"/Areas/API/Views/Test/PatientFlow.cshtml")]
    #nullable restore
    internal sealed class Areas_API_Views_Test_PatientFlow : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\PatientFlow.cshtml"
  
    ViewData["Title"] = "患者信息流程演示";
    Layout = "~/Views/Shared/_Layout.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""container mt-4"">
    <div class=""row"">
        <div class=""col-12"">
            <h2>中医经方推荐 - 完整流程演示</h2>
            <p class=""text-muted"">演示从患者信息录入到经方推荐的完整流程</p>
            
            <!-- 流程步骤 -->
            <div class=""card mb-4"">
                <div class=""card-body"">
                    <h5 class=""card-title"">流程步骤</h5>
                    <div class=""row"">
                        <div class=""col-md-3"">
                            <div class=""text-center"">
                                <div class=""btn btn-primary btn-circle mb-2"">1</div>
                                <p>患者信息录入</p>
                            </div>
                        </div>
                        <div class=""col-md-3"">
                            <div class=""text-center"">
                                <div class=""btn btn-secondary btn-circle mb-2"">2</div>
                                <p>创建会话</p>
                            </div>
                        </div>
                        <div class=""col-md-3"">");
            WriteLiteral(@"
                            <div class=""text-center"">
                                <div class=""btn btn-secondary btn-circle mb-2"">3</div>
                                <p>AI分析症状</p>
                            </div>
                        </div>
                        <div class=""col-md-3"">
                            <div class=""text-center"">
                                <div class=""btn btn-secondary btn-circle mb-2"">4</div>
                                <p>经方推荐</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 方案对比 -->
            <div class=""row"">
                <div class=""col-md-6"">
                    <div class=""card"">
                        <div class=""card-header bg-danger text-white"">
                            <h5 class=""mb-0"">❌ 旧方案 - URL参数传递</h5>
                        </div>
                        <div class=""card-body"">
                            <h6>问题：</h6>
           ");
            WriteLiteral(@"                 <ul>
                                <li>URL过长，不美观</li>
                                <li>参数暴露在地址栏，不安全</li>
                                <li>浏览器URL长度限制</li>
                                <li>中文参数编码问题</li>
                            </ul>
                            
                            <h6>示例URL：</h6>
                            <div class=""alert alert-warning"">
                                <small>
                                    /API/TCMPrescription/Index?name=张一鸣&gender=男&age=35&symptoms=近日失眠，面色暗黄，口干伴有耳鸣，健忘...
                                </small>
                            </div>
                            
                            <button class=""btn btn-outline-danger"" onclick=""testOldMethod()"">
                                测试旧方案
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class=""col-md-6"">
                    <div class=""card"">
               ");
            WriteLiteral(@"         <div class=""card-header bg-success text-white"">
                            <h5 class=""mb-0"">✅ 新方案 - Session会话</h5>
                        </div>
                        <div class=""card-body"">
                            <h6>优势：</h6>
                            <ul>
                                <li>URL简洁，只传递sessionId</li>
                                <li>患者信息安全存储在服务器</li>
                                <li>支持大量数据传递</li>
                                <li>更好的用户体验</li>
                            </ul>
                            
                            <h6>示例URL：</h6>
                            <div class=""alert alert-success"">
                                <small>
                                    /API/TCMPrescription/Index?sessionId=abc123
                                </small>
                            </div>
                            
                            <button class=""btn btn-outline-success"" onclick=""testNewMethod()"">
                                测试新方案
      ");
            WriteLiteral(@"                      </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速测试区域 -->
            <div class=""card mt-4"">
                <div class=""card-header"">
                    <h5 class=""mb-0"">快速测试</h5>
                </div>
                <div class=""card-body"">
                    <div class=""row"">
                        <div class=""col-md-4"">
                            <h6>1. 患者信息录入页面</h6>
                            <p class=""text-muted"">完整的患者信息录入表单</p>
                            <a href=""/API/TCMPrescription/PatientInfo"" class=""btn btn-primary"" target=""_blank"">
                                <i class=""fas fa-user-plus""></i>
                                打开录入页面
                            </a>
                        </div>
                        
                        <div class=""col-md-4"">
                            <h6>2. 直接测试推荐</h6>
                            <p class=""text-muted"">使用默认患者信息测试</p>
           ");
            WriteLiteral(@"                 <button class=""btn btn-success"" onclick=""createTestSession()"">
                                <i class=""fas fa-play""></i>
                                创建测试会话
                            </button>
                        </div>
                        
                        <div class=""col-md-4"">
                            <h6>3. API接口测试</h6>
                            <p class=""text-muted"">测试各个API接口</p>
                            <a href=""/API/Test/StreamDemo"" class=""btn btn-info"" target=""_blank"">
                                <i class=""fas fa-flask""></i>
                                API测试页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试结果显示 -->
            <div id=""testResult"" class=""card mt-4"" style=""display: none;"">
                <div class=""card-header"">
                    <h5 class=""mb-0"">测试结果</h5>
                </div>
                <div class=""card-body"">
     ");
            WriteLiteral("               <div id=\"resultContent\"></div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n");
            DefineSection("Styles", async() => {
                WriteLiteral(@"
    <style>
        .btn-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
");
            }
            );
            WriteLiteral("\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        function testOldMethod() {
            var symptoms = encodeURIComponent('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠');
            var url = `/API/TCMPrescription/Index?symptoms=${symptoms}&name=张一鸣&gender=男&age=35&department=心血管内科`;
            
            showResult('旧方案测试', `
                <div class=""alert alert-warning"">
                    <h6>生成的URL：</h6>
                    <p style=""word-break: break-all;"">${url}</p>
                    <p><strong>URL长度：</strong>${url.length} 字符</p>
                </div>
                <button class=""btn btn-primary"" onclick=""window.open('${url}', '_blank')"">
                    在新窗口中打开
                </button>
            `);
        }

        function testNewMethod() {
            createTestSession();
        }

        function createTestSession() {
            var patientData = {
                Name: '张一鸣',
                Gender: '男',
                Age: 35,
                HospitalNumber: 'ZY20230512',
                Department: '心血管内科',
     ");
                WriteLiteral(@"           Doctor: '李医生',
                Symptoms: '近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠',
                Diagnosis: '失眠'
            };

            showResult('新方案测试', `
                <div class=""alert alert-info"">
                    <i class=""fas fa-spinner fa-spin""></i>
                    正在创建患者会话...
                </div>
            `);

            $.ajax({
                url: '/API/TCMPrescription/CreatePatientSession',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(patientData),
                success: function(response) {
                    if (response.success) {
                        showResult('新方案测试', `
                            <div class=""alert alert-success"">
                                <h6>✅ 会话创建成功！</h6>
                                <p><strong>会话ID：</strong>${response.sessionId}</p>
                                <p><strong>简洁URL：</strong>${response.redirectUrl}</p>
                                <p><strong>URL");
                WriteLiteral(@"长度：</strong>${response.redirectUrl.length} 字符</p>
                            </div>
                            <div class=""row"">
                                <div class=""col-md-6"">
                                    <button class=""btn btn-success"" onclick=""window.open('${response.redirectUrl}', '_blank')"">
                                        <i class=""fas fa-external-link-alt""></i>
                                        打开推荐页面
                                    </button>
                                </div>
                                <div class=""col-md-6"">
                                    <button class=""btn btn-info"" onclick=""testGetPatientInfo('${response.sessionId}')"">
                                        <i class=""fas fa-info-circle""></i>
                                        测试获取患者信息
                                    </button>
                                </div>
                            </div>
                        `);
                    } else {
                     ");
                WriteLiteral(@"   showResult('新方案测试', `
                            <div class=""alert alert-danger"">
                                <h6>❌ 创建会话失败</h6>
                                <p>${response.message}</p>
                            </div>
                        `);
                    }
                },
                error: function() {
                    showResult('新方案测试', `
                        <div class=""alert alert-danger"">
                            <h6>❌ 网络错误</h6>
                            <p>无法连接到服务器</p>
                        </div>
                    `);
                }
            });
        }

        function testGetPatientInfo(sessionId) {
            $.get('/API/TCMPrescription/GetPatientInfo', { sessionId: sessionId })
                .done(function(response) {
                    if (response.success) {
                        var patient = response.data;
                        showResult('患者信息获取测试', `
                            <div class=""alert alert-success"">
                   ");
                WriteLiteral(@"             <h6>✅ 成功获取患者信息</h6>
                                <table class=""table table-sm"">
                                    <tr><td><strong>姓名：</strong></td><td>${patient.Name}</td></tr>
                                    <tr><td><strong>性别：</strong></td><td>${patient.Gender}</td></tr>
                                    <tr><td><strong>年龄：</strong></td><td>${patient.Age}岁</td></tr>
                                    <tr><td><strong>住院号：</strong></td><td>${patient.HospitalNumber}</td></tr>
                                    <tr><td><strong>科室：</strong></td><td>${patient.Department}</td></tr>
                                    <tr><td><strong>症状：</strong></td><td>${patient.Symptoms}</td></tr>
                                    <tr><td><strong>诊断：</strong></td><td>${patient.Diagnosis}</td></tr>
                                </table>
                            </div>
                        `);
                    } else {
                        showResult('患者信息获取测试', `
                         ");
                WriteLiteral(@"   <div class=""alert alert-danger"">
                                <h6>❌ 获取失败</h6>
                                <p>${response.message}</p>
                            </div>
                        `);
                    }
                })
                .fail(function() {
                    showResult('患者信息获取测试', `
                        <div class=""alert alert-danger"">
                            <h6>❌ 网络错误</h6>
                            <p>无法获取患者信息</p>
                        </div>
                    `);
                });
        }

        function showResult(title, content) {
            $('#testResult').show();
            $('#resultContent').html(`
                <h6>${title}</h6>
                ${content}
            `);
            
            // 滚动到结果区域
            $('html, body').animate({
                scrollTop: $('#testResult').offset().top
            }, 500);
        }
    </script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
