@{
    ViewData["Title"] = "中医经方推荐";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <style>
        .result-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .stream-content {
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .prescription-result {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-leaf"></i>
                        中医经方推荐系统
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 左侧输入区域 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="symptomsInput">症状描述</label>
                                <textarea id="symptomsInput" class="form-control" rows="8"
                                          placeholder="请详细描述患者症状，如：近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="sessionIdInput">会话ID（可选）</label>
                                <input type="text" id="sessionIdInput" class="form-control"
                                       placeholder="留空将自动生成新会话">
                            </div>
                            <div class="form-group">
                                <button id="submitBtn" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search"></i>
                                    获取经方推荐
                                </button>
                                <button id="clearBtn" class="btn btn-secondary btn-lg ml-2">
                                    <i class="fas fa-eraser"></i>
                                    清空
                                </button>
                            </div>
                        </div>

                        <!-- 右侧结果显示区域 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>推荐结果</label>
                                <div id="resultContainer" class="border rounded p-3" style="min-height: 300px; background-color: #f8f9fa;">
                                    <div id="loadingIndicator" class="text-center" style="display: none;">
                                        <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                        <p class="mt-2">正在分析症状，请稍候...</p>
                                    </div>
                                    <div id="resultContent" style="display: none;">
                                        <div id="streamContent"></div>
                                        <div id="finalResult" style="display: none;"></div>
                                    </div>
                                    <div id="emptyState" class="text-center text-muted">
                                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                        <p>请输入症状描述以获取经方推荐</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录区域 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history"></i>
                                        推荐历史
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="historyContainer">
                                        <p class="text-muted">暂无历史记录</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        $(document).ready(function() {
            let eventSource = null;
            let currentSessionId = '';

            // 提交按钮点击事件
            $('#submitBtn').click(function() {
                const symptoms = $('#symptomsInput').val().trim();
                if (!symptoms) {
                    toastr.warning('请输入症状描述');
                    return;
                }

                getPrescriptionRecommendation(symptoms);
            });

            // 清空按钮点击事件
            $('#clearBtn').click(function() {
                $('#symptomsInput').val('');
                $('#sessionIdInput').val('');
                $('#resultContainer #emptyState').show();
                $('#resultContainer #resultContent').hide();
                $('#streamContent').empty();
                $('#finalResult').empty().hide();
            });

            // 获取经方推荐
            function getPrescriptionRecommendation(symptoms) {
                const sessionId = $('#sessionIdInput').val().trim();

                // 显示加载状态
                $('#emptyState').hide();
                $('#resultContent').hide();
                $('#loadingIndicator').show();
                $('#submitBtn').prop('disabled', true);
                $('#streamContent').empty();
                $('#finalResult').empty().hide();

                // 关闭之前的连接
                if (eventSource) {
                    eventSource.close();
                }

                // 创建SSE连接
                const url = `/API/TCMPrescription/GetPrescriptionRecommendation`;
                const formData = new FormData();
                formData.append('symptoms', symptoms);
                formData.append('sessionId', sessionId);

                fetch(url, {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    // 显示结果容器
                    $('#loadingIndicator').hide();
                    $('#resultContent').show();

                    function readStream() {
                        reader.read().then(({ done, value }) => {
                            if (done) {
                                $('#submitBtn').prop('disabled', false);
                                return;
                            }

                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');

                            lines.forEach(line => {
                                if (line.startsWith('data: ')) {
                                    try {
                                        const data = JSON.parse(line.substring(6));
                                        handleStreamData(data);
                                    } catch (e) {
                                        console.error('解析数据失败:', e);
                                    }
                                }
                            });

                            readStream();
                        }).catch(error => {
                            console.error('读取流失败:', error);
                            $('#submitBtn').prop('disabled', false);
                            toastr.error('获取推荐失败: ' + error.message);
                        });
                    }

                    readStream();
                }).catch(error => {
                    console.error('请求失败:', error);
                    $('#loadingIndicator').hide();
                    $('#submitBtn').prop('disabled', false);
                    toastr.error('请求失败: ' + error.message);
                });
            }

            // 处理流式数据
            function handleStreamData(data) {
                if (data.error) {
                    toastr.error(data.error);
                    return;
                }

                if (data.type === 'chunk' && data.content) {
                    // 流式内容
                    $('#streamContent').append(data.content);
                } else if (data.type === 'complete' && data.output) {
                    // 完整结果
                    currentSessionId = data.sessionId || '';
                    $('#sessionIdInput').val(currentSessionId);

                    // 显示格式化的完整结果
                    $('#finalResult').html(formatPrescriptionResult(data.output)).show();

                    // 加载历史记录
                    loadHistory();
                } else if (data.code === 100) {
                    // 结束标志
                    $('#submitBtn').prop('disabled', false);
                }
            }

            // 格式化处方结果
            function formatPrescriptionResult(output) {
                // 这里可以根据实际输出格式进行解析和格式化
                return '<div class="alert alert-success"><pre>' + output + '</pre></div>';
            }

            // 加载历史记录
            function loadHistory() {
                if (!currentSessionId) return;

                $.get('/API/TCMPrescription/GetPrescriptionHistory', { sessionId: currentSessionId })
                    .done(function(response) {
                        if (response.code === 0 && response.data) {
                            displayHistory(response.data);
                        }
                    })
                    .fail(function() {
                        console.error('加载历史记录失败');
                    });
            }

            // 显示历史记录
            function displayHistory(history) {
                if (!history || history.length === 0) {
                    $('#historyContainer').html('<p class="text-muted">暂无历史记录</p>');
                    return;
                }

                let html = '<div class="list-group">';
                history.forEach(item => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">症状：${item.symptoms}</h6>
                                <small>${new Date(item.createTime).toLocaleString()}</small>
                            </div>
                            <p class="mb-1"><strong>推荐方剂：</strong>${item.prescription}</p>
                        </div>
                    `;
                });
                html += '</div>';

                $('#historyContainer').html(html);
            }
        });
    </script>
}
