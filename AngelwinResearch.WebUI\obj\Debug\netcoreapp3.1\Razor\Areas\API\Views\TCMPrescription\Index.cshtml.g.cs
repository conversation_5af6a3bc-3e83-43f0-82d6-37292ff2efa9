#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_API_Views_TCMPrescription_Index), @"mvc.1.0.view", @"/Areas/API/Views/TCMPrescription/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a", @"/Areas/API/Views/TCMPrescription/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_API_Views_TCMPrescription_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
       AngelwinResearch.WebUI.Areas.API.Controllers.TCMPrescriptionController.PatientInfoModel

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/user_icon.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("width", new global::Microsoft.AspNetCore.Html.HtmlString("100%"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("height", new global::Microsoft.AspNetCore.Html.HtmlString("100%"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
  
    Layout = null;
    var patientInfo = Model ?? new AngelwinResearch.WebUI.Areas.API.Controllers.TCMPrescriptionController.PatientInfoModel();

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\n<html lang=\"zh-CN\">\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a6654", async() => {
                WriteLiteral("\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>中医经方推荐</title>\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a7075", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a8276", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-image: url('~/images/bg_1.jpg');
            background-color: #f0f0f0; /* 图片加载失败时的备用背景色 */
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
            background-attachment: fixed; /* 固定背景，防止滚动时背景移动 */
            overflow: hidden;
        }

        /* 主页面内容样式 */
        .main-content {
            max-width: 980px;
            min-width: 635px;
            margin: 0 auto;
        }


        /* 头部患者信息 */
        .page_header {
            padding-top: 20px;
            margin-bottom: 5px;
        }

        .popup-header {
            padding: 15px;
            background: #5FB878;
            color: white;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .layui-card {
            border-radius: 6px;
        }

        .patie");
                WriteLiteral(@"nt-info {
            margin: 10px 0;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
        }

        .patient-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin: 15px;
        }

        .status span {
            font-size: 20px;
            font-weight: bold;
            margin-right: 10px;
        }

        .admission_information {
            display: flex;
            flex-direction: row;
        }

        /* 折叠卡片 */
        .layui-collapse {
            border: none;
        }

        .card_group {
            overflow-y: auto;
        }

        .layui-colla-item {
            border-radius: 6px !important;
            border-left: none !important;
            border-right: none !important;
            margin-bottom: 8px !important;
            overflow: hidden; /* 确保圆角生效 */
 ");
                WriteLiteral(@"           box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* 添加轻微阴影增强视觉效果 */
        }

            .layui-colla-item:first-child {
                border-top: none !important;
            }


        /* 当面板展开时，内容区域也应用圆角 */
        .layui-colla-content {
            padding: 15px;
            background-color: #f0f0f0;
        }

        /* 触发按钮 */
        .trigger-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 19891025;
        }



        /* 关闭按钮 */
        .close-btn {
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
        }

        /* 经方卡片内容 */

        .cards-container {
            border-radius: 6px;
            overflow: hidden;
        }

        .ai_group {
            background-color: #fff;
            padding: 15px;
            border-radius: 6px;
        }

        .table_group {
            margin-top: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 6px");
                WriteLiteral(@";
        }

        .formula-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .formula-info p {
            margin: 5px 0;
            line-height: 1.6;
        }

        /* 操作按钮容器 */
        .action-buttons {
            margin-top: 15px;
            text-align: right;
        }

        .color1 {
            color: #FF5722;
        }

        .color2 {
            color: #1E9FFF;
        }

        /* 加载状态样式 */
        .loading-content {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1E9FFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        ");
                WriteLiteral(@"@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 流式内容样式 */
        #streamContent {
            font-family: ""Microsoft YaHei"", Arial, sans-serif;
            line-height: 1.8;
            color: #333;
        }

        #streamContent h1, #streamContent h2, #streamContent h3 {
            color: #2c5530;
            margin: 15px 0 10px 0;
        }

        #streamContent p {
            margin: 8px 0;
        }

        #streamContent strong {
            color: #1E9FFF;
        }

        #streamContent table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        #streamContent table th,
        #streamContent table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        #streamContent table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        /* 响应式标题 */
        ");
                WriteLiteral("@media screen and (max-width: 768px) {\n            .main-title\n\n        {\n            font-size: 20px;\n        }\n\n        }\n\n        /* 在小屏幕上调整弹出组件大小 */\n        ");
                WriteLiteral(@"@media screen and (max-width: 480px) {
            .popup-container

        {
            width: calc(100% - 40px);
            bottom: 10px;
            right: 10px;
        }

        .trigger-btn {
            bottom: 10px;
            right: 10px;
        }

        }</style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a15791", async() => {
                WriteLiteral("\n    <div class=\"layui-container main-content\">\n        <div class=\"layui-row page_header\">\n            <h1 class=\"main-title\">中医经方推荐系统</h1>\n            <div class=\"patient-info\">\n                <div class=\"patient-avatar\">\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a16331", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                </div>\n                <div>\n                    <div class=\"status\"><span>");
                Write(
#nullable restore
#line 271 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
                                               patientInfo.Name

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span><span>");
                Write(
#nullable restore
#line 271 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
                                                                             patientInfo.Gender

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span><span>");
                Write(
#nullable restore
#line 271 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
                                                                                                             patientInfo.Age

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(" 岁</span></div>\n                    <div class=\"admission_information\"><p>住院号：<span>");
                Write(
#nullable restore
#line 272 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
                                                                     patientInfo.HospitalNumber

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span></p>丨<p>科室：<span>");
                Write(
#nullable restore
#line 272 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\TCMPrescription\Index.cshtml"
                                                                                                                        patientInfo.Department

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span></p></div>\n                </div>\n            </div>\n\n        </div>\n\n");
                WriteLiteral(@"
        <!-- 可折叠卡片容器 -->
        <div class=""cards-container"">
            <div class=""layui-collapse card_group"" id=""prescriptionCards"">
                <!-- 加载中状态 -->
                <div class=""loading-content"" id=""loadingContent"">
                    <div style=""text-align: center; padding: 50px;"">
                        <i class=""layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"" style=""font-size: 30px; color: #1E9FFF;""></i>
                        <p style=""margin-top: 10px; color: #666;"">正在分析症状，生成经方推荐...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>



    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4cfd6c2c7c099d9abde43cc98e3436a27f6ba1b0283a414e0f02968e1bb0500a20570", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
           layui.use(['element', 'layer','jquery','table'], function(){
                var element = layui.element,
                    layer = layui.layer
                    $ = layui.jquery,
                    table = layui.table;





                // 初始化折叠面板
                element.render('collapse');

                function setContainerH(){
                    var winh = $(window).height();
                    var topH = $('.page_header').height();
                    var containerH = winh - topH-40;

                    $('.card_group').css('height', containerH);
                }
                setContainerH();

                $(window).resize(function(){
                    setContainerH();
                });

                // 页面加载时获取患者信息并调用AI接口
                $(document).ready(function() {
                    var diagnosis = getUrlParameter('diagnosis');
                    var sympt=getUrlParameter('symptoms');
                    var symptoms = '症状：'+ sympt +'近日失眠，面色暗黄，口干");
                WriteLiteral(@"伴有耳鸣，健忘。初步辨病：'+ diagnosis;
                    getPrescriptionRecommendations(symptoms);
                    // if (sessionId) {
                    //     // 如果有sessionId，从服务器获取患者信息
                    //     loadPatientInfoFromSession(sessionId);
                    // } else {
                    //     // 直接使用传入的症状
                    //     if (symptoms) {
                    //         getPrescriptionRecommendations(symptoms);
                    //     }
                    // }
                });

                // 获取URL参数
                function getUrlParameter(name) {
                    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                    var results = regex.exec(location.search);
                    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
                }

                // 从Session加载患者信息
                function loadPatientInfoFromSession(sessionId) ");
                WriteLiteral(@"{
                    $.get('/API/TCMPrescription/GetPatientInfo', { sessionId: sessionId })
                        .done(function(response) {
                            if (response.success && response.data) {
                                var patientInfo = response.data;

                                // 更新页面上的患者信息
                                updatePatientInfoDisplay(patientInfo);

                                // 使用患者的症状信息调用AI接口
                                if (patientInfo.Symptoms) {
                                    getPrescriptionRecommendations(patientInfo.Symptoms);
                                }
                            } else {
                                layer.msg(response.message || '获取患者信息失败');
                                // 使用默认症状
                                getPrescriptionRecommendations('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠');
                            }
                        })
                        .fail(function() {
                            layer.msg('网络错误");
                WriteLiteral(@"，无法获取患者信息');
                            // 使用默认症状
                            getPrescriptionRecommendations('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠');
                        });
                }

                // 更新页面上的患者信息显示
                function updatePatientInfoDisplay(patientInfo) {
                    $('.status span:eq(0)').text(patientInfo.Name);
                    $('.status span:eq(1)').text(patientInfo.Gender);
                    $('.status span:eq(2)').text(patientInfo.Age + '岁');
                    $('.admission_information span:eq(0)').text(patientInfo.HospitalNumber);
                    $('.admission_information span:eq(1)').text(patientInfo.Department);
                }

                // 调用AI接口获取经方推荐
                function getPrescriptionRecommendations(symptoms) {
                    var source = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' +symptoms + '&sessionId=');
                    var allData = """";
                    var cardsInitialized = false");
                WriteLiteral(@";

                    source.onmessage = function (event) {
                        var result = JSON.parse(event.data);

                        if (result.error) {
                            showError(result.error);
                            source.close();
                            return;
                        }

                        if (result.type === 'chunk' && result.content) {
                            allData += result.content;

                            // 如果卡片还没有初始化，先创建空卡片结构
                            if (!cardsInitialized) {
                                initializeEmptyCards();
                                cardsInitialized = true;
                            }

                            // 实时更新卡片内容，实现打字机效果
                            updateCardsWithStreamContent(allData);

                        } else if (result.type === 'complete' && result.output) {
                            // 最终完整输出，确保所有内容都已填充
                            updateCardsWithStreamContent(result.output);
");
                WriteLiteral(@"                            source.close();
                        }
                    };

                    source.addEventListener('end', function (event) {
                         var result = JSON.parse(event.data);
                         console.log(""output:"",result.outputFull);
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        showError(""连接出现错误，请重试"");
                        source.close();
                    };
                }

                // 初始化空卡片结构
                function initializeEmptyCards() {
                    $('#loadingContent').hide();

                    var emptyCardsHtml = `
                        <div class=""layui-colla-item"">
                            <h2 class=""layui-colla-title"">AI推荐方剂 <span class=""layui-badge layui-bg-red"">推荐</span></h2>
                            <div class=""layui-colla-content layui-show"">
                                <div class=""ai_group"">");
                WriteLiteral(@"
                                    <div class=""formula-title"">由<span class=""color1"">中医知识库</span>生成</div>
                                    <div class=""formula-info"" id=""streamContent"">
                                        <div style=""color: #666; font-style: italic;"">正在生成经方推荐...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    $('#prescriptionCards').html(emptyCardsHtml);
                    element.render('collapse');
                }

                // 实时更新卡片内容
                function updateCardsWithStreamContent(content) {
                    try {
                        // 使用marked解析markdown内容
                        var htmlContent = marked.parse(content);

                        // 平滑更新内容
                        var $streamContent = $('#streamContent');
                        $streamContent.html(htmlContent);

                        // 自动滚动到底部");
                WriteLiteral(@"，显示最新内容
                        var container = $('.card_group')[0];
                        if (container && container.scrollHeight > container.clientHeight) {
                            container.scrollTop = container.scrollHeight;
                        }

                    } catch (error) {
                        console.error('解析markdown内容失败:', error);
                        // 如果markdown解析失败，直接显示原始内容
                        $('#streamContent').html('<pre>' + content + '</pre>');
                    }
                }

                // 解析AI输出并创建卡片
                function parseAndCreateCards(output) {
                    // 隐藏加载状态
                    $('#loadingContent').hide();

                    // 解析AI输出，这里需要根据实际AI输出格式调整
                    var cards = parseAIOutput(output);

                    var cardsHtml = '';
                    cards.forEach(function(card, index) {
                        cardsHtml += createCardHtml(card, index);
                    });

                    $('#presc");
                WriteLiteral(@"riptionCards').html(cardsHtml);
                    element.render('collapse');

                    // 渲染表格
                    cards.forEach(function(card, index) {
                        if (card.medicines && card.medicines.length > 0) {
                            renderMedicineTable(card.medicines, 'test' + (index + 1));
                        }
                    });
                }

                // 解析AI输出（根据实际AI输出格式调整）
                function parseAIOutput(output) {
                    // 这里是示例解析逻辑，需要根据实际AI输出格式调整
                    // 假设AI返回的是包含经方信息的文本，我们需要解析出结构化数据

                    // 临时示例：创建两个基于AI输出的卡片
                    return [
                        {
                            name: extractFromOutput(output, ""处方名称"") || ""AI推荐方剂一"",
                            source: ""中医知识库"",
                            composition: extractFromOutput(output, ""处方内容"") || extractFromOutput(output, ""组成"") || ""根据AI分析生成的药物组成"",
                            efficacy: extractFromOutput(output, ""功效"") || ""根据AI分析");
                WriteLiteral(@"生成的功效"",
                            indications: extractFromOutput(output, ""主治"") || ""根据AI分析生成的主治"",
                            usage: extractFromOutput(output, ""用法"") || ""根据AI分析生成的用法"",
                            applications: extractFromOutput(output, ""现代应用"") || ""根据AI分析生成的现代应用"",
                            analysis: extractFromOutput(output, ""分析内容"") || """",
                            medicines: [
                                { id: 1, name: '酸枣仁', dosage: '15g', price: '25'},
                                { id: 2, name: '茯苓', dosage: '12g', price: '18' },
                                { id: 3, name: '知母', dosage: '9g', price: '12' },
                                { id: 4, name: '川芎', dosage: '6g', price: '15' },
                                { id: 5, name: '甘草', dosage: '6g', price: '8' }
                            ]
                        },
                        {
                            name: ""AI推荐方剂二"",
                            source: ""deepseek"",
                            compositio");
                WriteLiteral(@"n: ""根据AI分析生成的第二个方剂组成"",
                            efficacy: ""根据AI分析生成的第二个方剂功效"",
                            indications: ""根据AI分析生成的第二个方剂主治"",
                            usage: ""根据AI分析生成的第二个方剂用法"",
                            applications: ""根据AI分析生成的第二个方剂现代应用"",
                            analysis: """",
                            medicines: []
                        }
                    ];
                }

                // 从AI输出中提取特定信息
                function extractFromOutput(output, keyword) {
                    // 简单的文本提取逻辑，可以根据需要改进
                    var regex = new RegExp(keyword + '[：:](.*?)(?=\\n|$)', 'i');
                    var match = output.match(regex);
                    return match ? match[1].trim() : null;
                }

                // 创建卡片HTML
                function createCardHtml(card, index) {
                    var badgeClass = index === 0 ? 'layui-bg-red' : '';
                    var badgeText = index === 0 ? '<span class=""layui-badge ' + badgeClass + '"">推荐</span>' :");
                WriteLiteral(@" '';
                    var showClass = index === 0 ? 'layui-show' : '';
                    var colorClass = card.source === '中医知识库' ? 'color1' : 'color2';

                    var tableHtml = '';
                    if (card.medicines && card.medicines.length > 0) {
                        tableHtml = `
                            <div class=""table_group"">
                                <div class=""formula-title"">经方合计</div>
                                <table class=""layui-hide"" id=""test${index + 1}"" lay-filter=""test${index + 1}""></table>
                            </div>
                        `;
                    }

                    var analysisHtml = '';
                    if (card.analysis) {
                        analysisHtml = `<p><strong>分析：</strong>${card.analysis}</p>`;
                    }

                    return `
                        <div class=""layui-colla-item"">
                            <h2 class=""layui-colla-title"">${card.name} ${badgeText}</h2>
                      ");
                WriteLiteral(@"      <div class=""layui-colla-content ${showClass}"">
                                <div class=""ai_group"">
                                    <div class=""formula-title"">由<span class=""${colorClass}"">${card.source}</span>生成</div>
                                    <div class=""formula-info"">
                                        <p><strong>组成：</strong>${card.composition}</p>
                                        <p><strong>功效：</strong>${card.efficacy}</p>
                                        <p><strong>主治：</strong>${card.indications}</p>
                                        <p><strong>用法：</strong>${card.usage}</p>
                                        ${card.applications ? '<p><strong>现代应用：</strong>' + card.applications + '</p>' : ''}
                                        ${analysisHtml}
                                    </div>
                                </div>
                                ${tableHtml}
                                <div class=""action-buttons"">
                       ");
                WriteLiteral(@"             <button class=""layui-btn layui-btn-sm layui-bg-blue""><i class=""layui-icon layui-icon-ok""></i>选择此方</button>
                                    <button class=""layui-btn layui-btn-sm layui-bg-red""><i class=""layui-icon layui-icon-close""></i>不采纳</button>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // 渲染药材表格
                function renderMedicineTable(medicines, tableId) {
                    table.render({
                        elem: '#' + tableId,
                        title: '费用汇总',
                        height: '320',
                        cols: [[
                            {field:'id', title:'ID', width:90, unresize: true, sort: true, totalRowText: '合计费用'},
                            {field: 'name', title: '药品', minWidth: 120},
                            {field: 'dosage', title: '计量', width: 150},
                            {field: 'price', title: '单价', width: 160, ");
                WriteLiteral(@"sort: true, totalRow: true,
                             templet: function (d) {
                                 return '￥' + d.price;
                             }}
                        ]],
                        data: medicines,
                        page: true,
                        limits: [5, 7, 10],
                        limit: 5,
                        totalRow: true,
                        response: {
                            statusCode: 200
                        }
                    });
                }

                // 显示错误信息
                function showError(message) {
                    $('#prescriptionCards').html(`
                        <div style=""text-align: center; padding: 50px;"">
                            <i class=""layui-icon layui-icon-close"" style=""font-size: 30px; color: #FF5722;""></i>
                            <p style=""margin-top: 10px; color: #FF5722;"">错误：${message}</p>
                            <button class=""layui-btn layui-btn-sm"" onclick=""location");
                WriteLiteral(".reload()\">重试</button>\n                        </div>\n                    `);\n                }\n        });\n\n    </script>\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.WebUI.Areas.API.Controllers.TCMPrescriptionController.PatientInfoModel> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
