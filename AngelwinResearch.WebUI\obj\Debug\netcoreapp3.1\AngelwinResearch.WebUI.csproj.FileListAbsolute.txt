D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\log4net.config
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\appsettings.json
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.exe
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.deps.json
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.runtimeconfig.json
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.runtimeconfig.dev.json
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.Views.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.WebUI.Views.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Azure.AI.OpenAI.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Azure.Core.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Common.DataSourceSupport.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\EPPlus.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Google.Protobuf.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Html2Markdown.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\HtmlAgilityPack.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Humanizer.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JetBrains.Annotations.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Json.More.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JsonConverter.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JsonConverter.Newtonsoft.Json.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JsonPointer.Net.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JsonSchema.Net.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\JsonSchema.Net.Generation.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\K4os.Compression.LZ4.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\K4os.Compression.LZ4.Streams.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\K4os.Hash.xxHash.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\log4net.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Cryptography.Internal.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Http.Features.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.JsonPatch.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Mvc.Razor.Extensions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Razor.Language.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Bcl.HashCode.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.Razor.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.Workspaces.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Data.SqlClient.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.DotNet.PlatformAbstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Json.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Physical.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileSystemGlobbing.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Http.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Identity.Core.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Identity.Stores.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Log4Net.AspNetCore.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Identity.Client.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.SemanticKernel.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.SemanticKernel.Abstractions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.SemanticKernel.Connectors.OpenAI.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.SemanticKernel.Core.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.Contracts.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.Core.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\dotnet-aspnet-codegenerator-design.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Win32.SystemEvents.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\MySql.Data.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Ubiety.Dns.Core.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Zstandard.Net.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Newtonsoft.Json.Bson.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Npgsql.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\NuGet.Frameworks.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Oracle.ManagedDataAccess.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Otp.NET.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\PDFtoImage.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\QRCoder.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Quartz.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\SharpToken.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\SkiaSharp.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Spire.Pdf.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Renci.SshNet.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\SshNet.Security.Cryptography.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Stef.Validation.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.ClientModel.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Composition.AttributedModel.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Composition.Convention.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Composition.Hosting.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Composition.Runtime.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Composition.TypedParts.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Configuration.ConfigurationManager.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Data.Odbc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Drawing.Common.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.IO.Pipelines.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Runtime.Caching.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Security.AccessControl.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Security.Cryptography.ProtectedData.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Security.Permissions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Security.Principal.Windows.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Text.Json.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Windows.Extensions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Xml.XPath.XmlDocument.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-musl-arm64\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-musl-x64\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-musl-x86\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-x86\native\libpdfium.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\osx-arm64\native\libpdfium.dylib
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\osx-x64\native\libpdfium.dylib
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\pdfium.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\pdfium.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\pdfium.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\Microsoft.Data.SqlClient.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp3.1\Microsoft.Win32.SystemEvents.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\sni.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\sni.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\sni.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libSkiaSharp.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libSkiaSharp.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-musl-x64\native\libSkiaSharp.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libSkiaSharp.so
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\osx\native\libSkiaSharp.dylib
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\libSkiaSharp.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libSkiaSharp.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libSkiaSharp.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\freebsd\lib\netcoreapp2.0\System.Data.Odbc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\linux\lib\netcoreapp2.0\System.Data.Odbc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\osx\lib\netcoreapp2.0\System.Data.Odbc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Data.Odbc.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp3.1\System.Drawing.Common.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp3.1\System.Drawing.Common.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Runtime.Caching.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netstandard2.0\System.Security.AccessControl.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\System.Security.Principal.Windows.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\System.Security.Principal.Windows.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp3.1\System.Windows.Extensions.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.Library.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Common.QuartzNet.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Common.Tools.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Sap.Data.Hana.Core.v2.1.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\AngelwinResearch.Library.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Common.QuartzNet.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Common.Tools.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.csproj.AssemblyReference.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.AssemblyInfoInputs.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.AssemblyInfo.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.csproj.CoreCompileInputs.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.MvcApplicationPartsAssemblyInfo.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.MvcApplicationPartsAssemblyInfo.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.RazorAssemblyInfo.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.RazorAssemblyInfo.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\AngelwinResearch.WebUI.StaticWebAssets.Manifest.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.TagHelpers.input.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.TagHelpers.output.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.RazorCoreGenerate.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\AgentInfo\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\ClinicDeptManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormFieldSetting\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormFieldsImport\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormsPad\DataProfiling.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormsPad\detail.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormsPad\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormsPad\MedicalCenterManage.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\DSpecificGroupManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\HospitalDepts\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\MedicalRecordUpLoad\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\MultiCentersManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\OrgManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\PromptInfo\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\CMIS\Views\AfterStructured\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\CMIS\Views\BeforeStructured\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\CMIS\Views\CMISDataDetails\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\BatchCaseExtraction\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\BatchInspectExtraction\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\CRFormTraceability\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\FollowUpAI\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\JsonGrid\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\NewPdfAI\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\PdfAI\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\PilotDS\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\Pilot\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\StructuredData\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\TextAIAnalysis\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\FollowUp\Views\FollowUpVisit\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\FollowUp\Views\SoundAnalysis\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\Classification\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\CRFDataDetails\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\DataCollectionManagement\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\FillInProgressSum\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index2.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index3.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index4.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\ResearchDataDetails\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\PatientDiscoveryManage\Views\ResearchProgress\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\MedicalRecordAI\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\PatientManage\DataCollect.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\PatientManage\DepartmentCollection.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\PatientManage\detail.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\PatientManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\ReportingManage\Views\RehabilitationDept\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Account\Login.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Account\ResetPwd.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\AnyReportDB.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\ChatDoc.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\detail.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\IndexAnyReport.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\IndexPdf.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Chat\PdfInfo.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\CRFManager\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Finding\AiFinding.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\followUpVisit\followUpVisitList.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\AanyReportGMS.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\AanyReportHYS.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\AanyReportTGJC.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\changelog.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\CRF.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\IndexDemo.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\ipadIndex.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\MenuHtmlPartialChild.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\NewTGJC.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\NewXBS.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\NewZLPG.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\NullPage.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Home\welcome.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\knowledgeBase\AiFinding.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\knowledgeBase\DS.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\knowledgeBase\knowledgeBase.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\knowledgeBase\searchTest.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Log\LoginLog.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Log\Logs.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Menu\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\OperationHistory\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Role\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\sample\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\sample\sampleIndex.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\User\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\conclusionExtract.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\conclusionExtract2.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\FillInProgressSum.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\Index2.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\InspectionReport.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\Package.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\ReportExtract.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\ResearchProgress.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\soundAnalysis.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\template.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\traceability.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\zjp.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\zjp2.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\vform\zlpg.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.RazorTargetAssemblyInfo.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.RazorTargetAssemblyInfo.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.Views.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.pdb
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.genruntimeconfig.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\CMIS\Views\CMISConfig\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\FluentFTP.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Xml.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormXmlImport\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Chat\Views\Chat\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\CMIS\Views\CMISDataDetails\detail.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\FollowUpTemplates\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Demo\Views\JiFangSuggest\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Report\Views\Report\BookStatistics.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Report\Views\Report\CrossReport.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Report\Views\Report\DeptBillAnalysisReport.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\Report\Views\Report\DrugStatistics.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\AngelwinResearch.WebUI.GeneratedMSBuildEditorConfig.editorconfig
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\AngelwinResearch.WebUI.StaticWebAssets.Pack.cache
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.AngelwinResearch.WebUI.Microsoft.AspNetCore.StaticWebAssets.props
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.AngelwinResearch.WebUI.props
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.AngelwinResearch.WebUI.props
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.AngelwinResearch.WebUI.props
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Angelwin.00381C30.Up2Date
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\bin\Debug\netcoreapp3.1\System.Net.WebSockets.WebSocketProtocol.dll
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\HospitalCRF\Views\DataRecord\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\HospitalCRF\Views\HospitalCRFDetails\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\BasicConfig\Views\CRFormFieldSetting\Index11.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\TCMPrescription\Index - 复制.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\TCMPrescription\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\TCMPrescription\Index1.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\Test\Index.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\Test\StreamDemo.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\Test\TCMTest.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\_ViewStart.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\TCMPrescription\PatientInfo.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\Test\PatientFlow.cshtml.g.cs
D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\obj\Debug\netcoreapp3.1\Razor\Areas\API\Views\Test\DecodeTest.cshtml.g.cs
