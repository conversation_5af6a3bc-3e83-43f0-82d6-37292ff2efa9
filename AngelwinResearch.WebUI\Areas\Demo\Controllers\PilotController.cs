﻿using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using Common.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Areas.Demo.Controllers
{
    [Area("Demo")]
    public class PilotController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinResearchDbContext db;
        public PilotController(IConfiguration configuration, AngelwinResearchDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }
        public IActionResult Index()
        {
            return View();
        }


        public IActionResult GetAgentInfo()
        {
            var mList = db.AgentInfos.Where(p => p.IsUsed == true).ToList().Select(p => new
            {
                AppId=p.AppId,
                AgentName=p.AgentName

            }).ToList();
            return Json(mList);

        }

        [SSE]
        public async Task<IActionResult> GetAgentSpeech(string Speech,string  xAppID)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            string mResult = "";
            var modelType = "Agent";
            string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"];
        
            string apiUrl =string.Format( Configuration[$"GPTSetting:{modelType}:apiUrl"], xAppID);
            var AppID= Configuration[$"GPTSetting:{modelType}:AppID"];
            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);
            httpClient.BaseAddress = new Uri(apiUrl);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                // Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            };
            using var APIResponse1 = await httpClient.SendAsync(request);
            string resultAI = "";
            if (APIResponse1.IsSuccessStatusCode)
            {
                var stream = await APIResponse1.Content.ReadAsStreamAsync();
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {

                    resultAI = reader.ReadToEnd();
                    LoggerHelper.WriteInfo("其他日志", $"Pilot创建新会话返回:" + resultAI);
                }
               var  mData = JsonConvert.DeserializeObject<Conversation>(resultAI);
                if (mData.code == "200")
                {
                    var mURL = "https://open.bigmodel.cn/api/llm-application/open/v2/application/generate_request_id";
                    dynamic requstDTO = new ExpandoObject();
                    var mdata = new Data();
                    mdata.id = "user";
                    mdata.type = "input";
                    mdata.name = "用户提问";
                    mdata.value = Speech;
                    var mlist = new List<Data>();
                    mlist.Add(mdata);
                    requstDTO.app_id = xAppID;
                    requstDTO.conversation_id = mData.data.conversation_id; // "956859";
                    requstDTO.key_value_pairs = mlist; // @"[{""id"": ""user"",""type"": ""input"",""name"":""用户提问"",""value"": ""你叫什么名字""} ]";
                    httpClient = new HttpClient();
                    httpClient.Timeout = TimeSpan.FromMinutes(5);
                    httpClient.BaseAddress = new Uri(mURL);
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    var requestBody = JsonConvert.SerializeObject(requstDTO);
                    request = new HttpRequestMessage(HttpMethod.Post, mURL)
                    {
                        Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                    };
                    using var APIResponse2 = await httpClient.SendAsync(request);
                    if (APIResponse2.IsSuccessStatusCode)
                    {
                         stream = await APIResponse2.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {

                            resultAI = reader.ReadToEnd();
                            LoggerHelper.WriteInfo("其他日志", $"Pilot创建对话返回请求:" + resultAI);

                        }
                        mData = JsonConvert.DeserializeObject<Conversation>(resultAI);
                        if (mData.code == "200")
                        {
                            var mgetdataURL = "https://open.bigmodel.cn/api/llm-application/open/v2/model-api/" + mData.data.id + "/sse-invoke";
                            httpClient = new HttpClient();
                            httpClient.Timeout = TimeSpan.FromMinutes(5);
                            httpClient.BaseAddress = new Uri(mgetdataURL);
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));


                            request = new HttpRequestMessage(HttpMethod.Post, mgetdataURL)
                            {
                                // Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                            };
                            using var APIResponse3 = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                            var ResponseContent = "";
                         

                            var contentStr = "";
                            if (APIResponse3.IsSuccessStatusCode)
                            {
                                string mAllLine = "";
                                var streamtemp = await APIResponse3.Content.ReadAsStreamAsync();
                                var streamReader = new StreamReader(streamtemp);
                                dynamic item = new ExpandoObject();
                          //   resultAI = streamReader.ReadToEnd();
                                var ddd = await streamReader.ReadLineAsync();
                                while (!streamReader.EndOfStream)
                                {
                                    var line = await streamReader.ReadLineAsync();
                                    mAllLine += line;
                                    if (!string.IsNullOrWhiteSpace(line))
                                    {
                                        if (line.StartsWith("id:"))
                                        {
                                           var mid = line.Substring(3, line.Length - 3);
                                            item.id = mid;
                                        }

                                        if (line != "data: [DONE]")
                                        {
                                            if (line.StartsWith("data:"))
                                            {
                                                line = line.Substring(5, line.Length - 5);
                                                if (line.Contains("msg"))
                                                {

                                                    var mDataTemp = JsonConvert.DeserializeObject<DataJson>(line);
                                                    
                                                    if (mDataTemp.msg!="" && mDataTemp.msg.Contains("desc_value")==false)
                                                    {
                                                        // mResult =  mDataTemp.extra_input.block_data.out_put.out_content;

                                                        mResult = mDataTemp.msg;
                                                        ResponseContent += mResult;
                                                        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, isAgentResult = false, AgentResult="", Id = item.id })}\n\n");
                                                        await response.Body.FlushAsync();
                                                    }
                                                }

                                               
                                                if (line.Contains("extra_input"))
                                                {

                                                    var mDataTemp = JsonConvert.DeserializeObject<DataJson>(line);

                                                    if(mDataTemp.extra_input.block_data.out_put != null)
                                                    {
                                                        if (mDataTemp.extra_input.block_data.out_put.out_content != "")
                                                        {
                                                            if (mDataTemp.extra_input.block_data.slice != null)
                                                            {
                                                                var mSlice = mDataTemp.extra_input.block_data.slice;

                                                                foreach (var item1 in mSlice)
                                                                {
                                                                    if (item1.slices != null)
                                                                    {
                                                                        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = true, AgentResult = item1.slices, Id = item.id })}\n\n");
                                                                        await response.Body.FlushAsync();
                                                                    }
                                                                }
                                                            }
                                                       
                                                           
                                                        }
                                                    }
                                                   
                                                    /*
                                                    if (mDataTemp.extra_input.node_data!=null && mDataTemp.extra_input.node_data.node_log_list!=null)
                                                    {
                                                        // mResult =  mDataTemp.extra_input.block_data.out_put.out_content;
                                                        foreach(var item1 in mDataTemp.extra_input.node_data.node_log_list)
                                                        {
                                                            if (item1.block_data.slice != null)
                                                            {
                                                                foreach (var a in item1.block_data.slice)
                                                                {
                                                                    if (a.slices != null)
                                                                    {
                                                                        foreach (var mTemp in a.slices)
                                                                        {
                                                                            mResult = mTemp.text;
                                                                            ResponseContent += mResult;
                                                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg ="", isAgentResult = false,AgentResult = mResult, Id = item.id })}\n\n");
                                                                            await response.Body.FlushAsync();
                                                                        }
                                                                    }


                                                                }
                                                            }
                                                           
                                                        }
                                                  
                                                       
                                                    }
                                                     */
                                                
                                                }

                                              
                                                if (line.Trim().Contains("msg") && line.Trim().Contains("data_value"))
                                                {
                                                    var delta = JObject.Parse(line);
                                                    var msg = delta.SelectToken("msg").ToString();
                                                    var mdata_value = JObject.Parse(msg).SelectToken("data_value").ToString();
                                                    var mstyle_value = JObject.Parse(msg).SelectToken("style_value").ToString();
                                                    var type = delta.SelectToken("type").ToString();
                                                    ////  var mDataTemp = JsonConvert.DeserializeObject<ChartData>(line);
                                                    // var delta= JObject.Parse(line);
                                                    item.data_value = mdata_value;
                                                    item.style_value = mstyle_value;
                                                    item.type = type;
                                                    await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, Id = item.id, isAgentResult = false,  AgentResult = "",chart = item })}\n\n");
                                                    await response.Body.FlushAsync();

                                                }

                                            }
                                               
                                        }
                                    }
                                }
                                LoggerHelper.WriteInfo("其他日志", $"Pilot返回会话内容:"+ mAllLine);
                            // return Json(new { code = 100, msg = mResult,chart=item });


                                var resultAll = new
                                {
                                    code = 100,
                                    chart = item,
                                    msg = ResponseContent,
                                   
                                    Id = item.id
                                };
                                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                                await Response.Body.FlushAsync();
                                return new EmptyResult();

                                /*
                                 stream = await APIResponse3.Content.ReadAsStreamAsync();
                                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                                {

                                    resultAI = reader.ReadToEnd();

                               
                                    var mResultTemp = resultAI.Split("event");


                                    for(int i= mResultTemp.Count() - 1; i> 0; i--)
                                    {
                                        var mTemp = mResultTemp[i];
                                        if (mTemp.Contains("data:") && mTemp.Contains("out_content"))
                                        {
                                            var mss = mTemp.Split("data:");
                                            var mTemp1 = mss[1];
                                            var mDataTemp = JsonConvert.DeserializeObject<DataJson>(mTemp1);
                                            if(mDataTemp.extra_input.block_data.out_put != null)
                                            {
                                                mResult = mResult + mDataTemp.extra_input.block_data.out_put.out_content;
                                            }
                                            break;

                                        }
                                        else
                                        {
                                          //  return Json(new { code = -100, msg = resultAI });
                                        }

                                    }
                                   
                                    return Json(new { code = 100, msg = mResult });


                                }
                                */
                            }
                            else
                            {
                                return Json(new { code = -100, msg = APIResponse3.Content });
                            }
                        }
                        else
                        {
                            return Json(new { code = -100, msg = mData.message });

                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = APIResponse2.Content });
                    }
                }   
                
                else
                {
                    return Json(new { code = -100, msg = mData.message });
                }
            }
            else
            {

                return Json(new { code = -100, msg = APIResponse1.Content });

            }
        
        }



        [SSE]
        public async Task<IActionResult> GetAgentSpeech1(string Speech, string xAppID)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            string mResult = "";
            var modelType = "AICO-Agent";
            string apiKey = "ApsQZTCgYrcIsYf681eIL2CBqC53soyR";// Configuration[$"GPTSetting:{modelType}:ApiKey"];
                xAppID = "";
            string apiUrl = string.Format(Configuration[$"GPTSetting:{modelType}:apiUrl"], xAppID);
            var AppID = Configuration[$"GPTSetting:{modelType}:AppID"];
            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);
            httpClient.BaseAddress = new Uri(apiUrl);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                // Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            };
            using var APIResponse1 = await httpClient.SendAsync(request);
            string resultAI = "";
            if (APIResponse1.IsSuccessStatusCode)
            {
                var stream = await APIResponse1.Content.ReadAsStreamAsync();
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {

                    resultAI = reader.ReadToEnd();
                    LoggerHelper.WriteInfo("其他日志", $"Pilot创建新会话返回:" + resultAI);
                }
                var mData = JsonConvert.DeserializeObject<Conversation>(resultAI);
                if (mData.code == "200")
                {
                    var mURL = "https://open.bigmodel.cn/api/llm-application/open/v2/application/generate_request_id";
                    dynamic requstDTO = new ExpandoObject();
                    var mdata = new Data();
                    mdata.id = "user";
                    mdata.type = "input";
                    mdata.name = "用户提问";
                    mdata.value = Speech;
                    var mlist = new List<Data>();
                    mlist.Add(mdata);
                    requstDTO.app_id = xAppID;
                    requstDTO.conversation_id = mData.data.conversation_id; // "956859";
                    requstDTO.key_value_pairs = mlist; // @"[{""id"": ""user"",""type"": ""input"",""name"":""用户提问"",""value"": ""你叫什么名字""} ]";
                    httpClient = new HttpClient();
                    httpClient.Timeout = TimeSpan.FromMinutes(5);
                    httpClient.BaseAddress = new Uri(mURL);
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    var requestBody = JsonConvert.SerializeObject(requstDTO);
                    request = new HttpRequestMessage(HttpMethod.Post, mURL)
                    {
                        Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                    };
                    using var APIResponse2 = await httpClient.SendAsync(request);
                    if (APIResponse2.IsSuccessStatusCode)
                    {
                        stream = await APIResponse2.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {

                            resultAI = reader.ReadToEnd();
                            LoggerHelper.WriteInfo("其他日志", $"Pilot创建对话返回请求:" + resultAI);

                        }
                        mData = JsonConvert.DeserializeObject<Conversation>(resultAI);
                        if (mData.code == "200")
                        {
                            var mgetdataURL = "https://open.bigmodel.cn/api/llm-application/open/v2/model-api/" + mData.data.id + "/sse-invoke";
                            httpClient = new HttpClient();
                            httpClient.Timeout = TimeSpan.FromMinutes(5);
                            httpClient.BaseAddress = new Uri(mgetdataURL);
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));


                            request = new HttpRequestMessage(HttpMethod.Post, mgetdataURL)
                            {
                                // Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                            };
                            using var APIResponse3 = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                            var ResponseContent = "";


                            var contentStr = "";
                            if (APIResponse3.IsSuccessStatusCode)
                            {
                                string mAllLine = "";
                                var streamtemp = await APIResponse3.Content.ReadAsStreamAsync();
                                var streamReader = new StreamReader(streamtemp);
                                dynamic item = new ExpandoObject();
                                //   resultAI = streamReader.ReadToEnd();
                                var ddd = await streamReader.ReadLineAsync();
                                while (!streamReader.EndOfStream)
                                {
                                    var line = await streamReader.ReadLineAsync();
                                    mAllLine += line;
                                    if (!string.IsNullOrWhiteSpace(line))
                                    {
                                        if (line.StartsWith("id:"))
                                        {
                                            var mid = line.Substring(3, line.Length - 3);
                                            item.id = mid;
                                        }

                                        if (line != "data: [DONE]")
                                        {
                                            if (line.StartsWith("data:"))
                                            {
                                                line = line.Substring(5, line.Length - 5);
                                                if (line.Contains("msg"))
                                                {

                                                    var mDataTemp = JsonConvert.DeserializeObject<DataJson>(line);

                                                    if (mDataTemp.msg != "" && mDataTemp.msg.Contains("desc_value") == false)
                                                    {
                                                        // mResult =  mDataTemp.extra_input.block_data.out_put.out_content;

                                                        mResult = mDataTemp.msg;
                                                        ResponseContent += mResult;
                                                        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, isAgentResult = false, AgentResult = "", Id = item.id })}\n\n");
                                                        await response.Body.FlushAsync();
                                                    }
                                                }


                                                if (line.Contains("extra_input"))
                                                {

                                                    var mDataTemp = JsonConvert.DeserializeObject<DataJson>(line);

                                                    if (mDataTemp.extra_input.block_data.out_put != null)
                                                    {
                                                        if (mDataTemp.extra_input.block_data.out_put.out_content != "")
                                                        {
                                                            if (mDataTemp.extra_input.block_data.slice != null)
                                                            {
                                                                var mSlice = mDataTemp.extra_input.block_data.slice;

                                                                foreach (var item1 in mSlice)
                                                                {
                                                                    if (item1.slices != null)
                                                                    {
                                                                        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = "", isAgentResult = true, AgentResult = item1.slices, Id = item.id })}\n\n");
                                                                        await response.Body.FlushAsync();
                                                                    }
                                                                }
                                                            }


                                                        }
                                                    }

                                                    /*
                                                    if (mDataTemp.extra_input.node_data!=null && mDataTemp.extra_input.node_data.node_log_list!=null)
                                                    {
                                                        // mResult =  mDataTemp.extra_input.block_data.out_put.out_content;
                                                        foreach(var item1 in mDataTemp.extra_input.node_data.node_log_list)
                                                        {
                                                            if (item1.block_data.slice != null)
                                                            {
                                                                foreach (var a in item1.block_data.slice)
                                                                {
                                                                    if (a.slices != null)
                                                                    {
                                                                        foreach (var mTemp in a.slices)
                                                                        {
                                                                            mResult = mTemp.text;
                                                                            ResponseContent += mResult;
                                                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg ="", isAgentResult = false,AgentResult = mResult, Id = item.id })}\n\n");
                                                                            await response.Body.FlushAsync();
                                                                        }
                                                                    }


                                                                }
                                                            }
                                                           
                                                        }
                                                  
                                                       
                                                    }
                                                     */

                                                }


                                                if (line.Trim().Contains("msg") && line.Trim().Contains("data_value"))
                                                {
                                                    var delta = JObject.Parse(line);
                                                    var msg = delta.SelectToken("msg").ToString();
                                                    var mdata_value = JObject.Parse(msg).SelectToken("data_value").ToString();
                                                    var mstyle_value = JObject.Parse(msg).SelectToken("style_value").ToString();
                                                    var type = delta.SelectToken("type").ToString();
                                                    ////  var mDataTemp = JsonConvert.DeserializeObject<ChartData>(line);
                                                    // var delta= JObject.Parse(line);
                                                    item.data_value = mdata_value;
                                                    item.style_value = mstyle_value;
                                                    item.type = type;
                                                    await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { code = "100", msg = mResult, Id = item.id, isAgentResult = false, AgentResult = "", chart = item })}\n\n");
                                                    await response.Body.FlushAsync();

                                                }

                                            }

                                        }
                                    }
                                }
                                LoggerHelper.WriteInfo("其他日志", $"Pilot返回会话内容:" + mAllLine);
                                // return Json(new { code = 100, msg = mResult,chart=item });


                                var resultAll = new
                                {
                                    code = 100,
                                    chart = item,
                                    msg = ResponseContent,

                                    Id = item.id
                                };
                                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                                await Response.Body.FlushAsync();
                                return new EmptyResult();

                                /*
                                 stream = await APIResponse3.Content.ReadAsStreamAsync();
                                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                                {

                                    resultAI = reader.ReadToEnd();

                               
                                    var mResultTemp = resultAI.Split("event");


                                    for(int i= mResultTemp.Count() - 1; i> 0; i--)
                                    {
                                        var mTemp = mResultTemp[i];
                                        if (mTemp.Contains("data:") && mTemp.Contains("out_content"))
                                        {
                                            var mss = mTemp.Split("data:");
                                            var mTemp1 = mss[1];
                                            var mDataTemp = JsonConvert.DeserializeObject<DataJson>(mTemp1);
                                            if(mDataTemp.extra_input.block_data.out_put != null)
                                            {
                                                mResult = mResult + mDataTemp.extra_input.block_data.out_put.out_content;
                                            }
                                            break;

                                        }
                                        else
                                        {
                                          //  return Json(new { code = -100, msg = resultAI });
                                        }

                                    }
                                   
                                    return Json(new { code = 100, msg = mResult });


                                }
                                */
                            }
                            else
                            {
                                return Json(new { code = -100, msg = APIResponse3.Content });
                            }
                        }
                        else
                        {
                            return Json(new { code = -100, msg = mData.message });

                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = APIResponse2.Content });
                    }
                }

                else
                {
                    return Json(new { code = -100, msg = mData.message });
                }
            }
            else
            {

                return Json(new { code = -100, msg = APIResponse1.Content });

            }

        }

        public  class ChartData
        {
            public string msg { get; set; }
            public string data_value { get; set; }
        }

        public class Data
        {
            public string id { get; set; }
            public string type { get; set; }
            public string value { get; set; }

            public string name { get; set; }

            public string msg { get; set; }
            public string data_value { get; set; }
        }

        public class NodeData
        {
            public int node_type { get; set; }
            public string node_id { get; set; }

            public string node_name { get; set; }
            public string node_status { get; set; }

            public string node_dur { get; set; }

            public List<ExtraInput> node_log_list { get; set; }
        }

        public class BlockData
        {
            public string input { get; set; }

            public string block_status { get; set; }

            public string block_type { get; set; }
            public OutPut out_put { get; set; }

            public string error_msg { get; set; }
            public string block_dur { get; set; }

            public List< Slice>  slice { get; set; }
        }

        public  class Document
        {
            public string id { get; set; }
            public string name { get; set; }

            public string @url { get; set; }
        }
        public class Slice
        {
            public Document document { get; set; }
            public List<Slices> slices { get; set; }
        }
        public class slockdatalice
        {

            public   List<Slice> slices {get;set; }
        }
        public class Slices
        {
            public string text { get; set; }

            public string document_id { get; set; }
            public string position { get; set; }
            public string line { get; set; }
            public string sheet_name { get; set; }

            public string score { get; set; }
            public string source { get; set; }

        }
             
        public class OutPut
        {
            public string function_exec_time { get; set; }
            public string model_exec_time { get; set; }
            public string out_content { get; set; }
        }

        public class Usage
        {
            public int completion_tokens { get; set; }
            public int prompt_tokens { get; set; }
            public int total_tokens { get; set; }
        }
        public class ExtraInput
        {
            public string request_id { get; set; }
            public string node_id { get; set; }

            public string push_type { get; set; }

            public NodeData node_data { get; set; }

            public BlockData block_data { get; set; }
        }

        public class DataJson
        {
            public string msg { get; set; }
            public ExtraInput extra_input { get; set; }
            public Usage usage { get; set; }
        }

        public class Seeinvoke
        {
            public string id { get; set; }
            public string @event { get; set; }

            public DataJson data { get; set; }
        }

        public class ConversationData
        {
            public string conversation_id { get; set; }
            public string id { get; set; }
        }
        public class Conversation
        {
            public ConversationData data { get; set; }
            public string message { get; set; }
            public string timestamp { get; set; }
            public string code { get; set; }
        }

    }
}
