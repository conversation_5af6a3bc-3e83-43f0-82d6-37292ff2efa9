#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\TCMTest.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "364b54b2cb067bfe0afc29306ec0c667cef587642480536b678afc922461a28a"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_API_Views_Test_TCMTest), @"mvc.1.0.view", @"/Areas/API/Views/Test/TCMTest.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"364b54b2cb067bfe0afc29306ec0c667cef587642480536b678afc922461a28a", @"/Areas/API/Views/Test/TCMTest.cshtml")]
    #nullable restore
    internal sealed class Areas_API_Views_Test_TCMTest : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\API\Views\Test\TCMTest.cshtml"
  
    ViewData["Title"] = "中医经方推荐测试";
    Layout = "~/Views/Shared/_Layout.cshtml";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<div class=""container mt-4"">
    <div class=""row"">
        <div class=""col-12"">
            <h2>中医经方推荐系统测试</h2>
            <div class=""card"">
                <div class=""card-body"">
                    <h5 class=""card-title"">测试不同症状的经方推荐</h5>

                    <div class=""mb-3"">
                        <label for=""symptomsInput"" class=""form-label"">症状描述：</label>
                        <textarea id=""symptomsInput"" class=""form-control"" rows=""3""
                                  placeholder=""请输入患者症状描述..."">近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠</textarea>
                    </div>

                    <div class=""mb-3"">
                        <h6>快速测试症状：</h6>
                        <button class=""btn btn-outline-primary btn-sm me-2"" onclick=""setSymptoms('近日失眠，面色暗黄，口干伴有耳鸣，健忘。初步辨病：失眠')"">失眠症状</button>
                        <button class=""btn btn-outline-primary btn-sm me-2"" onclick=""setSymptoms('胸闷气短，心悸怔忡，舌淡苔白，脉细弱')"">心悸症状</button>
                        <button class=""btn btn-outline-primary btn-sm me-2"" onclick=""set");
            WriteLiteral(@"Symptoms('头痛眩晕，耳鸣如蝉，腰膝酸软，舌红少苔')"">头痛眩晕</button>
                        <button class=""btn btn-outline-primary btn-sm me-2"" onclick=""setSymptoms('咳嗽痰多，胸闷气喘，舌苔白腻，脉滑')"">咳嗽痰多</button>
                    </div>

                    <div class=""mb-3"">
                        <button id=""testBtn"" class=""btn btn-primary"">
                            <i class=""fas fa-play""></i>
                            打开经方推荐页面
                        </button>
                        <button id=""testApiBtn"" class=""btn btn-secondary ms-2"">
                            <i class=""fas fa-flask""></i>
                            测试API连接
                        </button>
                    </div>

                    <div id=""testResult"" class=""mt-3"" style=""display: none;"">
                        <div class=""alert alert-info"">
                            <h6>测试结果：</h6>
                            <pre id=""resultContent""></pre>
                        </div>
                    </div>
                </div>
            </div>
        </");
            WriteLiteral("div>\n    </div>\n</div>\n\n");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
    <script>
        function setSymptoms(symptoms) {
            $('#symptomsInput').val(symptoms);
        }

        $(document).ready(function() {
            $('#testBtn').click(function() {
                var symptoms = $('#symptomsInput').val().trim();
                if (!symptoms) {
                    alert('请输入症状描述');
                    return;
                }

                // 打开经方推荐页面，传递症状参数
                var url = '/API/TCMPrescription/Index?symptoms=' + encodeURIComponent(symptoms);
                window.open(url, '_blank');
            });

            $('#testApiBtn').click(function() {
                var symptoms = $('#symptomsInput').val().trim();
                if (!symptoms) {
                    alert('请输入症状描述');
                    return;
                }

                // 测试API连接
                var source = new EventSource('/API/TCMPrescription/GetPrescriptionRecommendation?symptoms=' + encodeURIComponent(symptoms) + '&sessionId=');
                var allData = """";
  ");
                WriteLiteral(@"              var startTime = new Date();

                $('#testResult').show();
                $('#resultContent').text('正在测试API连接...\n开始时间: ' + startTime.toLocaleTimeString());

                source.onmessage = function (event) {
                    var result = JSON.parse(event.data);
                    var currentTime = new Date();
                    var elapsed = (currentTime - startTime) / 1000;

                    if (result.error) {
                        $('#resultContent').text('错误: ' + result.error + '\n耗时: ' + elapsed + '秒');
                        source.close();
                        return;
                    }

                    if (result.type === 'chunk' && result.content) {
                        allData += result.content;
                        $('#resultContent').text('接收到流式数据...\n已接收字符数: ' + allData.length + '\n耗时: ' + elapsed + '秒\n\n最新片段:\n' + result.content + '\n\n累积内容预览:\n' + allData.substring(Math.max(0, allData.length - 200)));
                    } else if (resul");
                WriteLiteral(@"t.type === 'complete' && result.output) {
                        $('#resultContent').text('API测试成功!\n总耗时: ' + elapsed + '秒\n总字符数: ' + result.output.length + '\n\n完整输出:\n' + result.output.substring(0, 500) + (result.output.length > 500 ? '...(截断)' : ''));
                        source.close();
                    }
                };

                source.addEventListener('end', function (event) {
                    source.close();
                }, false);

                source.onerror = function (event) {
                    var currentTime = new Date();
                    var elapsed = (currentTime - startTime) / 1000;
                    $('#resultContent').text('API连接错误\n耗时: ' + elapsed + '秒\n请检查网络连接和服务器状态');
                    source.close();
                };

                // 30秒超时
                setTimeout(function() {
                    if (source.readyState !== EventSource.CLOSED) {
                        source.close();
                        $('#resultContent').text($('#resultCont");
                WriteLiteral("ent\').text() + \'\\n\\n测试超时(30秒)\');\n                    }\n                }, 30000);\n            });\n        });\n    </script>\n");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
