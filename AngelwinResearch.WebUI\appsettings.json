{
  "AppSettings": {
    "SiteTitle": "全院CRF(EDC)平台",
    "OnlyAllowOnePlaceLogin": "false",
    "ExpiredTime": 180,
    "APIUrl": "http://192.168.2.114:21003/v1/",
    "modelType": "qwen3",
    "modelType2": "glm-4", //AI随访模型
    "VLmodelType": "qwen-vl", //多模态模型
    "PageURL": "http://8.131.88.44:5032/HealthCareManage/HealthCareProgress1/Index1?PID=",
    "Page2URL": "http://8.131.88.44:5032/Package/Package/Index",
    "Page3URL": "http://8.131.88.44:5032/HealthCareManage/InspectionReport/Index",
    "ConnectionString": "Server=8.131.88.44;Database=AngelwinResearch;UID=sa;PWD=*******;Connection Timeout=600;Packet Size=512;",
    "ChromaTop": 5,
    "SendType": "DINGTALK", // <!--消息推送配置 钉钉|企业微信  DINGTALK|WECHAT-->,
    "AnyReportUrl": "https://www.zolf.top:444/dmp/", //  https://www.zolf.top:444/  http://8.131.88.44:9905
    //"AITransferTokenUrl": "http://192.168.1.73:56033/Client/Token/GetToken", //<!--110.85.54.33:38080T-->,
    "AITransferAPIUrl": "http://110.85.54.33:38080/Research/GetResult", //<!--110.85.54.33:38080T-->,
    "clientCode": "Angelwin002",
    "clientName": "监管平台",
    "IsLoginAnyReport": "0", //0-不登录；1-登录
    "CRFDelayMaxTime": 3000, //毫秒
    "CRFDelayMinTime": 2000, //毫秒
    "HanaConnectionString": "Server=218.205.89.179:30015;UserID=AnyReport;Password=AnyReport#025",
    "DmsConnectionString": "server = 8.131.88.44; port = 9906; database = dms; uid = root; pwd = root; CharSet = utf8;",
    "XuFeiAPPID": "8740d028",
    "XuFeiAPI_KEY": "a6df6bf8f6ca924630e283395c1625a7",
    "YZS_Appseret": "39f40f64e85abf4f676715fbd2ac1c80",
    "XuFei_TTS_API_Key": "14af5e16c0b2cca34b1f84b3e5383a88",
    "XuFei_TTS_API_Secret": "YzI3NjU2MWQ4Y2YwMDNhN2ZkN2MxYjMx",
    "YZSAPI_KEY": "qor4olkxxw4cj7bf7wiln2n3k3k5spyv3s3fnfq3",
    "RoleSplitIntervalTime": 15, //大模型区分语音间隔时间秒
    "IsThink": "false",
    "ModelList": "qwq,qwen,qwen-vl,deepseek-v3,glm-4",
    "AutoMedicalIntervalTime": 30 //根据语音自动转写病历文书时间
  },
  "Data": {
    "DefaultConnection": {
      "ConnectionString": "Server=8.131.88.44;Database=AngelwinResearch;UID=sa;PWD=*******;Connection Timeout=600;Packet Size=512;",
      //"ConnectionString": "Server=47.109.133.106;Database=AngelwinResearch;UID=sa;PWD=***********;Connection Timeout=600;Packet Size=512;"
    }
  },
  //https://gateway.ai.cloudflare.com/v1/7d235fd970a38c2cf79fcef5d10fad3c/openai/openai;
  //https://api.openai.com/v1/
  "GPTSetting": {
    "Agent": {
      "ApiKey": "42f2f4c06a0242b98ac4b0058ba2d435.nJxSu2ic0h47Djuo",
      "apiUrl": "https://open.bigmodel.cn/api/llm-application/open/v2/application/{0}/conversation"
    },
    "deepseek-Agent": {
      "ApiKey": "app-LmVkcmq7rJwHCTL6Jcph3dSP",
      "apiUrl": "http://31d85d9887.zicp.vip/v1/workflows/run"
    },
    "difyAgent": {
      "ApiKey": "app-f4lrWbqIsZ2tGgLj2bVJUK1n",
      "apiUrl": "http://************/v1/" //31d85d9887.zicp.vip
    },
    "gpt-4": {
      "ApiKey": "********************************************************",
      "Model": "gpt-4o",
      "apiUrl": "https://gateway.ai.cloudflare.com/v1/7d235fd970a38c2cf79fcef5d10fad3c/openai/openai/",
      "MaxTokens": 8000,
      "WebProxy": "http://127.0.0.1:7890"
    },
    "qwq": { //千问深度推理
      "ApiKey": "ApiKey",
      "apiUrl": "http://************:11434/v1/",
      "Model": "qwq:32b",
      "IsThink": 1,
      "IsMultimodal": 0,
      "MaxTokens": 8000
    },
    "qwen": {
      "ApiKey": "***********************************",
      "apiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1/",
      "Model": "qwen2.5-72b-instruct",
      "IsThink": 0,
      "IsMultimodal": 0,
      "MaxTokens": 8000
    },
    "qwen-vl": {
      "ApiKey": "***********************************",
      "apiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1/",
      "Model": "qwen2.5-vl-72b-instruct",
      "IsThink": 0,
      "IsMultimodal": 1,
      "MaxTokens": 8000
    },
    "qwen3": {
      "ApiKey": "***********************************",
      "apiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1/",
      "Model": "qwen3-235b-a22b-instruct-2507",
      "IsThink": 0,
      "IsMultimodal": 0,
      "MaxTokens": 8000
    },
    "glm-4": {
      "ApiKey": "42f2f4c06a0242b98ac4b0058ba2d435.nJxSu2ic0h47Djuo",
      "apiUrl": "https://open.bigmodel.cn/api/paas/v4/",
      "Model": "GLM-4-Plus1",
      "IsThink": 0,
      "IsMultimodal": 0,
      "MaxTokens": 4095
    },
    "glm-4v": {
      "ApiKey": "42f2f4c06a0242b98ac4b0058ba2d435.nJxSu2ic0h47Djuo",
      "apiUrl": "https://open.bigmodel.cn/api/paas/v4/",
      "Model": "glm-4v",
      "IsThink": 0,
      "IsMultimodal": 1,
      "MaxTokens": 4095
    },
    "deepseek-r1": {
      "ApiKey": "",
      "apiUrl": "http://************:11434/v1/",
      "Model": "deepseek-r1:32b",
      "IsThink": 1,
      "IsMultimodal": 0,
      "MaxTokens": 65000
    },
    "deepseek-v3": {
      "ApiKey": "***********************************",
      "apiUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1/",
      "Model": "deepseek-v3",
      "IsThink": 0,
      "IsMultimodal": 0,
      "MaxTokens": 8000
    },
    //关键字：领导人姓名、政治言论、色情暴力、反社会和谐；
    "FilterKeywords": "共产党,习近,近平,习大,李强,天朝,中国,中南海,色情,暴力,恐怖,成人电影,枪,军火,俄乌,乌俄,VPN,傻逼,测试测试测试测试"


  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*"
}
