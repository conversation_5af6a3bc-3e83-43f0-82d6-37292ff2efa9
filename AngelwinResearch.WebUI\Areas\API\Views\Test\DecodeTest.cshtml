@{
    ViewData["Title"] = "HTML实体解码测试";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>HTML实体解码测试</h2>
            <p class="text-muted">测试中文字符的HTML实体编码解码功能</p>
            
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">解码测试工具</h5>
                    
                    <div class="form-group mb-3">
                        <label for="encodedText" class="form-label">HTML实体编码文本：</label>
                        <textarea id="encodedText" class="form-control" rows="3" 
                                  placeholder="请输入HTML实体编码的文本...">&#x8FD1;&#x65E5;&#x5931;&#x7720;&#xFF0C;&#x9762;&#x8272;&#x6697;&#x9EC4;&#xFF0C;&#x53E3;&#x5E72;&#x4F34;&#x6709;&#x8033;&#x9E23;&#xFF0C;&#x5065;&#x5FD8;</textarea>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="decodedText" class="form-label">解码后文本：</label>
                        <textarea id="decodedText" class="form-control" rows="3" readonly></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <button class="btn btn-primary" onclick="testDecode()">
                            <i class="fas fa-code"></i>
                            解码测试
                        </button>
                        <button class="btn btn-secondary" onclick="clearAll()">
                            <i class="fas fa-eraser"></i>
                            清空
                        </button>
                        <button class="btn btn-success" onclick="testWithTCM()">
                            <i class="fas fa-play"></i>
                            测试中医推荐
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>常见HTML实体编码示例：</h6>
                            <div class="list-group">
                                <button class="list-group-item list-group-item-action" onclick="setExample('&#x8FD1;&#x65E5;&#x5931;&#x7720;')">
                                    <strong>近日失眠：</strong> &#x8FD1;&#x65E5;&#x5931;&#x7720;
                                </button>
                                <button class="list-group-item list-group-item-action" onclick="setExample('&#x9762;&#x8272;&#x6697;&#x9EC4;')">
                                    <strong>面色暗黄：</strong> &#x9762;&#x8272;&#x6697;&#x9EC4;
                                </button>
                                <button class="list-group-item list-group-item-action" onclick="setExample('&#x53E3;&#x5E72;&#x4F34;&#x6709;&#x8033;&#x9E23;')">
                                    <strong>口干伴有耳鸣：</strong> &#x53E3;&#x5E72;&#x4F34;&#x6709;&#x8033;&#x9E23;
                                </button>
                                <button class="list-group-item list-group-item-action" onclick="setExample('&#x5065;&#x5FD8;&#xFF0C;&#x5FC3;&#x60B8;&#x6016;&#x5FE1;')">
                                    <strong>健忘，心悸怔忡：</strong> &#x5065;&#x5FD8;&#xFF0C;&#x5FC3;&#x60B8;&#x6016;&#x5FE1;
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>URL编码测试：</h6>
                            <div class="form-group">
                                <label>生成测试URL：</label>
                                <div class="input-group">
                                    <input type="text" id="testUrl" class="form-control" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyUrl()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <button class="btn btn-info btn-sm" onclick="openTestUrl()">
                                    <i class="fas fa-external-link-alt"></i>
                                    在新窗口中测试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 解码结果显示 -->
            <div id="decodeResult" class="card mt-4" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">解码结果详情</h5>
                </div>
                <div class="card-body">
                    <div id="resultDetails"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 解码HTML实体编码
        function decodeHtmlEntities(text) {
            if (!text) return text;
            
            // 创建一个临时的textarea元素来解码HTML实体
            var textarea = document.createElement('textarea');
            textarea.innerHTML = text;
            var decoded = textarea.value;
            
            // 如果还有十六进制编码，继续解码
            if (decoded.indexOf('&#x') !== -1) {
                decoded = decoded.replace(/&#x([0-9A-Fa-f]+);/g, function(match, hex) {
                    return String.fromCharCode(parseInt(hex, 16));
                });
            }
            
            // 如果还有十进制编码，继续解码
            if (decoded.indexOf('&#') !== -1) {
                decoded = decoded.replace(/&#(\d+);/g, function(match, dec) {
                    return String.fromCharCode(parseInt(dec, 10));
                });
            }
            
            return decoded;
        }

        function testDecode() {
            var encodedText = $('#encodedText').val();
            var decodedText = decodeHtmlEntities(encodedText);
            
            $('#decodedText').val(decodedText);
            
            // 生成测试URL
            var testUrl = `/API/TCMPrescription/Index?symptoms=${encodeURIComponent(decodedText)}`;
            $('#testUrl').val(testUrl);
            
            // 显示详细结果
            showDecodeResult(encodedText, decodedText, testUrl);
        }

        function showDecodeResult(original, decoded, url) {
            var details = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>原始编码文本：</h6>
                        <div class="alert alert-warning">
                            <pre style="white-space: pre-wrap; margin: 0;">${original}</pre>
                        </div>
                        <p><strong>字符数：</strong>${original.length}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>解码后文本：</h6>
                        <div class="alert alert-success">
                            <pre style="white-space: pre-wrap; margin: 0;">${decoded}</pre>
                        </div>
                        <p><strong>字符数：</strong>${decoded.length}</p>
                    </div>
                </div>
                
                <h6>生成的测试URL：</h6>
                <div class="alert alert-info">
                    <small style="word-break: break-all;">${url}</small>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="window.open('${url}', '_blank')">
                        <i class="fas fa-external-link-alt"></i>
                        在新窗口中测试
                    </button>
                </div>
            `;
            
            $('#resultDetails').html(details);
            $('#decodeResult').show();
            
            // 滚动到结果区域
            $('html, body').animate({
                scrollTop: $('#decodeResult').offset().top
            }, 500);
        }

        function setExample(text) {
            $('#encodedText').val(text);
            testDecode();
        }

        function clearAll() {
            $('#encodedText').val('');
            $('#decodedText').val('');
            $('#testUrl').val('');
            $('#decodeResult').hide();
        }

        function testWithTCM() {
            var decodedText = $('#decodedText').val();
            if (!decodedText) {
                alert('请先进行解码测试');
                return;
            }
            
            var url = `/API/TCMPrescription/Index?symptoms=${encodeURIComponent(decodedText)}`;
            window.open(url, '_blank');
        }

        function copyUrl() {
            var url = $('#testUrl').val();
            if (url) {
                navigator.clipboard.writeText(url).then(function() {
                    alert('URL已复制到剪贴板');
                }).catch(function() {
                    // 降级方案
                    $('#testUrl').select();
                    document.execCommand('copy');
                    alert('URL已复制到剪贴板');
                });
            }
        }

        function openTestUrl() {
            var url = $('#testUrl').val();
            if (url) {
                window.open(url, '_blank');
            } else {
                alert('请先进行解码测试生成URL');
            }
        }

        // 页面加载时自动测试默认示例
        $(document).ready(function() {
            testDecode();
        });
    </script>
}
